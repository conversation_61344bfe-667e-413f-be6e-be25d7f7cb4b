/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 活动状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Activity, ActivityListParams } from '../types/activity'
import { getActivities, getActivityDetail } from '../api/activities'

export const useActivityStore = defineStore('activity', () => {
  // 状态
  const activities = ref<Activity[]>([])
  const currentActivity = ref<Activity | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const publishedActivities = computed(() => 
    activities.value.filter(activity => activity.status === 1)
  )

  const currentActivityCanRegister = computed(() => {
    if (!currentActivity.value) return false
    
    const now = new Date()
    const endTime = new Date(currentActivity.value.end_time)
    const registrationEnd = currentActivity.value.registration_end 
      ? new Date(currentActivity.value.registration_end) 
      : endTime
    
    return (
      currentActivity.value.status === 1 &&
      now <= registrationEnd &&
      now <= endTime &&
      (currentActivity.value.quota === 0 || 
       currentActivity.value.registered_count < currentActivity.value.quota)
    )
  })

  // 方法
  const fetchActivities = async (params?: ActivityListParams) => {
    loading.value = true
    error.value = null
    
    try {
      const data = await getActivities(params)
      activities.value = data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取活动列表失败'
      console.error('获取活动列表失败:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchActivityDetail = async (id: string | number) => {
    loading.value = true
    error.value = null
    
    try {
      const data = await getActivityDetail(id)
      currentActivity.value = data
      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取活动详情失败'
      console.error('获取活动详情失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearCurrentActivity = () => {
    currentActivity.value = null
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    activities,
    currentActivity,
    loading,
    error,
    
    // 计算属性
    publishedActivities,
    currentActivityCanRegister,
    
    // 方法
    fetchActivities,
    fetchActivityDetail,
    clearCurrentActivity,
    clearError
  }
})
