/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 短信记录模型
 */

const BaseModel = require('./BaseModel');

class SmsLog extends BaseModel {
  static tableName = 'sms_logs';

  /**
   * 创建短信记录
   */
  static async create(data) {
    const { phone, type, content, status, yunpian_response, event_id } = data;
    
    const query = `
      INSERT INTO ${this.tableName} 
      (phone, type, content, status, yunpian_response, event_id, created_at)
      VALUES (?, ?, ?, ?, ?, ?, NOW())
    `;
    
    const result = await this.execute(query, [
      phone, 
      type, 
      content, 
      status, 
      yunpian_response, 
      event_id
    ]);
    
    return { id: result.insertId, ...data };
  }

  /**
   * 根据手机号查询短信记录
   */
  static async findByPhone(phone, limit = 10) {
    const query = `
      SELECT * FROM ${this.tableName} 
      WHERE phone = ? 
      ORDER BY created_at DESC 
      LIMIT ?
    `;
    
    return await this.query(query, [phone, limit]);
  }

  /**
   * 根据活动ID查询短信记录
   */
  static async findByEventId(eventId, limit = 100) {
    const query = `
      SELECT * FROM ${this.tableName} 
      WHERE event_id = ? 
      ORDER BY created_at DESC 
      LIMIT ?
    `;
    
    return await this.query(query, [eventId, limit]);
  }

  /**
   * 获取短信发送统计
   */
  static async getStatistics(eventId = null) {
    let query = `
      SELECT 
        type,
        status,
        COUNT(*) as count
      FROM ${this.tableName}
    `;
    
    const params = [];
    
    if (eventId) {
      query += ' WHERE event_id = ?';
      params.push(eventId);
    }
    
    query += ' GROUP BY type, status ORDER BY type, status';
    
    return await this.query(query, params);
  }

  /**
   * 删除过期的短信记录（保留最近30天）
   */
  static async cleanupOldRecords() {
    const query = `
      DELETE FROM ${this.tableName} 
      WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
    `;
    
    return await this.execute(query);
  }
}

module.exports = SmsLog;
