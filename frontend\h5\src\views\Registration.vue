<!--
<AUTHOR>
@license http://www.178188.xyz
@lastmodify 2025年8月4日
模块说明: 活动报名页面
-->

<template>
  <div class="registration">
    <!-- 导航栏 -->
    <van-nav-bar
      title="活动报名"
      left-arrow
      @click-left="goBack"
      fixed
    />
    
    <!-- 内容区域 -->
    <div class="content">
      <van-loading v-if="activityStore.loading" class="loading" />
      
      <div v-else-if="activity" class="registration-content">
        <!-- 活动信息 -->
        <div class="activity-info">
          <h2 class="activity-title">{{ activity.title }}</h2>
          <div class="activity-meta">
            <div class="meta-item">
              <van-icon name="clock-o" />
              <span>{{ formatTime(activity.start_time) }}</span>
            </div>
            <div class="meta-item">
              <van-icon name="location-o" />
              <span>{{ activity.address }}</span>
            </div>
          </div>
        </div>

        <!-- 报名表单 -->
        <div class="form-section">
          <van-form @submit="handleSubmit">
            <van-cell-group inset>
              <van-field
                v-model="form.phone"
                name="phone"
                label="手机号"
                placeholder="请输入手机号"
                :rules="phoneRules"
                maxlength="11"
                type="tel"
              />
              
              <van-field
                v-model="form.code"
                name="code"
                label="验证码"
                placeholder="请输入验证码"
                :rules="codeRules"
                maxlength="6"
                type="number"
              >
                <template #button>
                  <van-button
                    size="small"
                    :disabled="!canSendCode"
                    :loading="registrationStore.loading && sendingCode"
                    @click="handleSendCode"
                  >
                    {{ getCodeButtonText() }}
                  </van-button>
                </template>
              </van-field>
            </van-cell-group>

            <!-- 提交按钮 -->
            <div class="submit-section">
              <van-button
                type="primary"
                native-type="submit"
                :loading="registrationStore.loading && !sendingCode"
                :disabled="!canSubmit"
                block
                round
              >
                确认报名
              </van-button>
            </div>
          </van-form>
        </div>

        <!-- 温馨提示 -->
        <div class="tips-section">
          <van-notice-bar
            left-icon="info-o"
            text="请确保手机号正确，报名成功后将收到确认短信"
          />
        </div>
      </div>

      <!-- 错误状态 -->
      <van-empty
        v-else-if="activityStore.error"
        image="error"
        :description="activityStore.error"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showDialog } from 'vant'
import { useActivityStore } from '../stores/activity'
import { useRegistrationStore } from '../stores/registration'
import { useCountdown } from '../composables/useCountdown'
import { useFormValidation } from '../composables/useFormValidation'
import { formatTime as utilFormatTime } from '../utils'
import type { RegistrationForm } from '../types/registration'

const route = useRoute()
const router = useRouter()
const activityStore = useActivityStore()
const registrationStore = useRegistrationStore()

// 组合式函数
const countdown = useCountdown(60)
const { validatePhone, validateCode, hasErrors, clearAllErrors } = useFormValidation()

// 响应式数据
const form = ref<RegistrationForm>({
  phone: '',
  code: '',
  eventId: route.params.id as string
})

const sendingCode = ref(false)

// 表单验证规则
const phoneRules = [
  { required: true, message: '请输入手机号' },
  { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
]

const codeRules = [
  { required: true, message: '请输入验证码' },
  { pattern: /^\d{6}$/, message: '请输入6位数字验证码' }
]

// 计算属性
const activity = computed(() => activityStore.currentActivity)

const canSendCode = computed(() => {
  return (
    validatePhone(form.value.phone) &&
    !countdown.isActive.value &&
    !registrationStore.loading
  )
})

const canSubmit = computed(() => {
  return (
    validatePhone(form.value.phone) &&
    validateCode(form.value.code) &&
    !registrationStore.loading
  )
})

// 方法
const loadActivity = async () => {
  const id = route.params.id as string
  try {
    await activityStore.fetchActivityDetail(id)
    
    // 检查是否可以报名
    if (!activityStore.currentActivityCanRegister) {
      showDialog({
        title: '提示',
        message: '当前活动不可报名',
        confirmButtonText: '返回'
      }).then(() => {
        goBack()
      })
    }
  } catch (error) {
    console.error('加载活动详情失败:', error)
  }
}

const handleSendCode = async () => {
  if (!canSendCode.value) return

  sendingCode.value = true

  try {
    await registrationStore.sendCode(form.value.phone, form.value.eventId)
    countdown.start(60) // 开始倒计时
    showToast('验证码已发送')
  } catch (error) {
    showToast(error instanceof Error ? error.message : '发送失败')
  } finally {
    sendingCode.value = false
  }
}

const handleSubmit = async () => {
  if (!canSubmit.value) return
  
  try {
    await registrationStore.submitForm(form.value)
    
    // 保存手机号到本地存储
    localStorage.setItem('lastRegistrationPhone', form.value.phone)
    
    showToast('报名成功！')
    
    // 跳转到报名状态页
    router.replace(`/registration-status/${form.value.eventId}`)
    
  } catch (error) {
    showToast(error instanceof Error ? error.message : '报名失败')
  }
}

const goBack = () => {
  router.back()
}

const formatTime = (timeStr: string) => {
  return utilFormatTime(timeStr, 'datetime')
}

const getCodeButtonText = () => {
  if (countdown.isActive.value) {
    return `${countdown.count.value}s`
  }
  return '发送验证码'
}

// 生命周期
onMounted(() => {
  loadActivity()
})

onUnmounted(() => {
  // 清理状态
  registrationStore.clearError()
  clearAllErrors()
  countdown.stop()
})
</script>

<style scoped>
.registration {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.content {
  padding-top: 46px; /* 导航栏高度 */
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.registration-content {
  padding: 16px;
}

.activity-info {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.activity-title {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 12px;
  line-height: 1.4;
}

.activity-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #646566;
}

.meta-item .van-icon {
  margin-right: 8px;
  color: #969799;
}

.form-section {
  margin-bottom: 16px;
}

.submit-section {
  margin-top: 24px;
  padding: 0 16px;
}

.tips-section {
  margin-top: 16px;
}
</style>
