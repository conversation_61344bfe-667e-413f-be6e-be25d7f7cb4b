<!--
<AUTHOR>
@license http://www.178188.xyz
@lastmodify 2025年8月4日
模块说明: 活动报名页面
-->

<template>
  <div class="bg-gray-50 min-h-screen">
    <!-- 头部导航 -->
    <header class="bg-white shadow-sm sticky top-0 z-10">
      <div class="flex items-center px-4 py-3">
        <button class="back-btn mr-3 p-1 rounded-full hover:bg-gray-100 transition-colors" @click="goBack">
          <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">活动报名</h1>
      </div>
    </header>

    <!-- 加载状态 -->
    <div v-if="activityStore.loading" class="text-center py-8">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      <p class="mt-2 text-gray-500">加载中...</p>
    </div>

    <!-- 活动信息卡片 -->
    <div v-else-if="activity" class="bg-white mx-4 mt-4 rounded-lg shadow-sm border border-gray-200 p-4">
      <h2 class="text-lg font-semibold text-gray-900 mb-2">{{ activity.title }}</h2>
      <div class="space-y-1 text-sm text-gray-600">
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          <span>{{ formatTime(activity.start_time) }}</span>
        </div>
        <div class="flex items-center">
          <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
          <span>{{ activity.address }}</span>
        </div>
      </div>
    </div>

    <!-- 报名表单 -->
    <div v-if="activity" class="bg-white mx-4 mt-4 rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-6">填写报名信息</h3>
      
      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- 手机号输入 -->
        <div>
          <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
            手机号码 <span class="text-red-500">*</span>
          </label>
          <input 
            v-model="form.phone"
            type="tel" 
            id="phone" 
            name="phone"
            class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-3 focus:ring-blue-100 transition-all"
            placeholder="请输入您的手机号码"
            maxlength="11"
            required
            @input="formatPhoneInput"
          >
          <div v-if="errors.phone" class="error-message text-red-500 text-sm mt-1">{{ errors.phone }}</div>
        </div>

        <!-- 验证码输入 -->
        <div>
          <label for="verification-code" class="block text-sm font-medium text-gray-700 mb-2">
            验证码 <span class="text-red-500">*</span>
          </label>
          <div class="flex space-x-3">
            <input 
              v-model="form.code"
              type="text" 
              id="verification-code" 
              name="code"
              class="form-input flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-3 focus:ring-blue-100 transition-all"
              placeholder="请输入验证码"
              maxlength="6"
              required
              @input="formatCodeInput"
            >
            <button 
              type="button" 
              :class="[
                'px-6 py-3 rounded-lg font-medium transition-colors whitespace-nowrap',
                canSendCode ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              ]"
              :disabled="!canSendCode"
              @click="handleSendCode"
            >
              {{ getCodeButtonText() }}
            </button>
          </div>
          <div v-if="errors.code" class="error-message text-red-500 text-sm mt-1">{{ errors.code }}</div>
        </div>

        <!-- 提交按钮 -->
        <button 
          type="submit" 
          :class="[
            'w-full py-4 rounded-lg text-lg font-semibold transition-colors',
            canSubmit ? 'bg-blue-500 text-white hover:bg-blue-600 active:bg-blue-700' : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          ]"
          :disabled="!canSubmit || registrationStore.loading"
        >
          {{ registrationStore.loading ? '提交中...' : '确认报名' }}
        </button>
      </form>
    </div>

    <!-- 温馨提示 -->
    <div class="mx-4 mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
      <div class="flex items-start">
        <svg class="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <div class="text-sm text-blue-700">
          <div class="font-medium mb-1">温馨提示</div>
          <ul class="space-y-1 text-blue-600">
            <li>• 验证码有效期为5分钟，请及时输入</li>
            <li>• 每个手机号只能报名一次</li>
            <li>• 报名成功后将收到确认短信</li>
            <li>• 如有疑问请联系客服：400-123-4567</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 成功提示模态框 -->
    <div v-if="showSuccessModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center px-4 z-50">
      <div class="bg-white rounded-lg p-6 max-w-sm w-full">
        <div class="text-center">
          <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">报名成功！</h3>
          <p class="text-gray-600 text-sm mb-6">
            您已成功报名参加活动，确认短信已发送至您的手机，请注意查收。
          </p>
          <button 
            class="w-full bg-blue-500 text-white py-3 rounded-lg font-medium hover:bg-blue-600 transition-colors"
            @click="closeSuccessModal"
          >
            确定
          </button>
        </div>
      </div>
    </div>

    <!-- 底部安全区域 -->
    <div class="h-8"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useActivityStore } from '../stores/activity'
import { useRegistrationStore } from '../stores/registration'
import { useCountdown } from '../composables/useCountdown'
import type { RegistrationForm } from '../types/registration'

const route = useRoute()
const router = useRouter()
const activityStore = useActivityStore()
const registrationStore = useRegistrationStore()
const countdown = useCountdown(60)

// 响应式数据
const form = ref<RegistrationForm>({
  phone: '',
  code: '',
  eventId: route.params.id as string
})

const errors = ref<Record<string, string>>({})
const showSuccessModal = ref(false)

// 计算属性
const activity = computed(() => activityStore.currentActivity)

const canSendCode = computed(() => {
  return (
    validatePhone(form.value.phone) &&
    !countdown.isActive.value &&
    !registrationStore.loading
  )
})

const canSubmit = computed(() => {
  return (
    validatePhone(form.value.phone) &&
    validateCode(form.value.code) &&
    !registrationStore.loading
  )
})

// 方法
const validatePhone = (phone: string) => {
  return /^1[3-9]\d{9}$/.test(phone)
}

const validateCode = (code: string) => {
  return /^\d{6}$/.test(code)
}

const formatPhoneInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  let value = target.value.replace(/\D/g, '')
  if (value.length > 11) {
    value = value.slice(0, 11)
  }
  form.value.phone = value

  // 清除错误信息
  if (errors.value.phone) {
    delete errors.value.phone
  }
}

const formatCodeInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  let value = target.value.replace(/\D/g, '')
  if (value.length > 6) {
    value = value.slice(0, 6)
  }
  form.value.code = value

  // 清除错误信息
  if (errors.value.code) {
    delete errors.value.code
  }
}

const showError = (field: string, message: string) => {
  errors.value[field] = message
  setTimeout(() => {
    delete errors.value[field]
  }, 3000)
}

const loadActivity = async () => {
  const id = route.params.id as string
  try {
    await activityStore.fetchActivityDetail(id)

    // 检查是否可以报名
    if (!activityStore.currentActivityCanRegister) {
      alert('当前活动不可报名')
      goBack()
    }
  } catch (error) {
    console.error('加载活动详情失败:', error)
  }
}

const handleSendCode = async () => {
  if (!canSendCode.value) return

  if (!validatePhone(form.value.phone)) {
    showError('phone', '请输入正确的手机号码')
    return
  }

  try {
    await registrationStore.sendCode(form.value.phone, form.value.eventId)
    countdown.start(60)
    alert('验证码已发送到您的手机，请注意查收')
  } catch (error) {
    alert(error instanceof Error ? error.message : '发送失败')
  }
}

const handleSubmit = async () => {
  if (!canSubmit.value) return

  // 验证手机号
  if (!validatePhone(form.value.phone)) {
    showError('phone', '请输入正确的手机号码')
    return
  }

  // 验证验证码
  if (!validateCode(form.value.code)) {
    showError('code', '请输入6位数字验证码')
    return
  }

  try {
    await registrationStore.submitForm(form.value)

    // 保存手机号到本地存储
    localStorage.setItem('lastRegistrationPhone', form.value.phone)

    // 显示成功模态框
    showSuccessModal.value = true

  } catch (error) {
    alert(error instanceof Error ? error.message : '报名失败')
  }
}

const closeSuccessModal = () => {
  showSuccessModal.value = false
  // 跳转到报名状态页
  router.replace(`/registration-status/${form.value.eventId}`)
}

const goBack = () => {
  router.back()
}

const formatTime = (timeStr: string) => {
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getCodeButtonText = () => {
  if (countdown.isActive.value) {
    return `${countdown.count.value}秒后重发`
  }
  return '获取验证码'
}

// 生命周期
onMounted(() => {
  loadActivity()
})

onUnmounted(() => {
  registrationStore.clearError()
  countdown.stop()
})
</script>

<style scoped>
/* 微信浏览器兼容性 */
body {
  -webkit-text-size-adjust: 100%;
}

.back-btn:active {
  transform: scale(0.95);
}

.submit-btn:active {
  transform: scale(0.98);
}

/* 输入框焦点样式 */
.form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 错误提示样式 */
.error-message {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
