/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 创建管理员账户脚本
 */

const { mysqlPool } = require('../src/config/database');
const bcrypt = require('bcryptjs');

async function createAdmin() {
  try {
    console.log('开始创建管理员账户...');

    // 检查admins表是否存在
    const [tables] = await mysqlPool.execute(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'admins'
    `);

    if (tables.length === 0) {
      console.log('创建admins表...');
      
      // 创建admins表
      await mysqlPool.execute(`
        CREATE TABLE admins (
          id INT AUTO_INCREMENT PRIMARY KEY,
          username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
          password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
          name VARCHAR(100) NOT NULL COMMENT '姓名',
          status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
          last_login_at DATETIME NULL COMMENT '最后登录时间',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          INDEX idx_username (username),
          INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表'
      `);
      
      console.log('admins表创建成功');
    }

    // 检查是否已存在admin用户
    const [existingAdmin] = await mysqlPool.execute(
      'SELECT id FROM admins WHERE username = ?',
      ['admin']
    );

    if (existingAdmin.length > 0) {
      console.log('admin用户已存在，更新密码...');
      
      // 更新密码
      const hashedPassword = await bcrypt.hash('123456', 10);
      await mysqlPool.execute(
        'UPDATE admins SET password_hash = ?, updated_at = NOW() WHERE username = ?',
        [hashedPassword, 'admin']
      );
      
      console.log('admin用户密码已更新为: 123456');
    } else {
      console.log('创建admin用户...');
      
      // 创建管理员账户
      const hashedPassword = await bcrypt.hash('123456', 10);
      
      await mysqlPool.execute(`
        INSERT INTO admins (username, password_hash, name, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, NOW(), NOW())
      `, ['admin', hashedPassword, '系统管理员', 1]);
      
      console.log('admin用户创建成功');
    }

    console.log('管理员账户信息:');
    console.log('用户名: admin');
    console.log('密码: 123456');
    console.log('请登录后及时修改密码！');

  } catch (error) {
    console.error('创建管理员账户失败:', error);
  } finally {
    process.exit(0);
  }
}

// 运行脚本
createAdmin();
