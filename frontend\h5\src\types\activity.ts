/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 活动相关类型定义
 */

/**
 * 活动状态枚举
 */
export enum ActivityStatus {
  DRAFT = 0,      // 草稿
  PUBLISHED = 1,  // 已发布
  ENDED = 2       // 已结束
}

/**
 * 活动数据结构
 */
export interface Activity {
  id: number
  title: string
  description: string
  target_audience: string
  address: string
  start_time: string
  end_time: string
  registration_start?: string
  registration_end?: string
  quota: number
  status: ActivityStatus
  qr_code_url?: string
  registered_count: number
  created_at: string
  updated_at: string
}

/**
 * 活动列表查询参数
 */
export interface ActivityListParams {
  page?: number
  limit?: number
  status?: ActivityStatus
  keyword?: string
}

/**
 * 活动卡片显示数据
 */
export interface ActivityCard {
  id: number
  title: string
  target_audience: string
  start_time: string
  end_time: string
  address: string
  status: ActivityStatus
  registered_count: number
  quota: number
  canRegister: boolean
}
