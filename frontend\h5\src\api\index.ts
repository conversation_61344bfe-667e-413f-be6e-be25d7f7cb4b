/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: API服务基础配置
 */

import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

// API响应数据结构
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  code?: number
  timestamp?: string
}

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 可以在这里添加token等认证信息
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response
    
    // 如果响应成功
    if (data.success) {
      return response
    }
    
    // 如果响应失败，抛出错误
    const error = new Error(data.message || '请求失败')
    return Promise.reject(error)
  },
  (error) => {
    console.error('响应错误:', error)
    
    // 处理网络错误
    if (!error.response) {
      error.message = '网络连接失败，请检查网络'
    } else {
      // 处理HTTP错误状态码
      const { status } = error.response
      switch (status) {
        case 400:
          error.message = '请求参数错误'
          break
        case 401:
          error.message = '未授权访问'
          break
        case 403:
          error.message = '禁止访问'
          break
        case 404:
          error.message = '请求的资源不存在'
          break
        case 500:
          error.message = '服务器内部错误'
          break
        default:
          error.message = error.response.data?.message || '请求失败'
      }
    }
    
    return Promise.reject(error)
  }
)

export default api
