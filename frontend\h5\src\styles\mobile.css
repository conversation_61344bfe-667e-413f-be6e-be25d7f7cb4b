/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 移动端适配样式
 */

/* 移动端基础适配 */
@media screen and (max-width: 750px) {
  html {
    font-size: 14px;
  }
  
  body {
    font-size: 14px;
    line-height: 1.5;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 375px) {
  html {
    font-size: 12px;
  }
}

/* 大屏幕适配 */
@media screen and (min-width: 751px) {
  html {
    font-size: 16px;
  }
  
  #app {
    max-width: 750px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  }
}

/* 防止页面缩放 */
input, textarea, select {
  font-size: 16px !important;
  transform-origin: left top;
}

/* iOS Safari 适配 */
.ios-fix {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 微信浏览器适配 */
.wechat-browser {
  /* 隐藏微信浏览器的底部工具栏影响 */
  padding-bottom: 20px;
}

/* 横屏适配 */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .van-nav-bar {
    height: 40px;
  }
  
  .van-nav-bar__title {
    font-size: 16px;
  }
  
  .content {
    padding-top: 40px;
  }
}

/* 高分辨率屏幕适配 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .activity-card {
    border: 0.5px solid #ebedf0;
  }
}

/* 触摸优化 */
.touchable {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* 滚动优化 */
.scroll-container {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

/* 输入框优化 */
.van-field__control {
  -webkit-appearance: none;
  appearance: none;
}

/* 按钮优化 */
.van-button {
  -webkit-tap-highlight-color: transparent;
}

/* 卡片阴影优化 */
.activity-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

@media (prefers-color-scheme: dark) {
  .activity-card {
    box-shadow: 0 2px 12px rgba(255, 255, 255, 0.05);
  }
}
