/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 加载状态组合式函数
 */

import { ref } from 'vue'

export function useLoading(initialState = false) {
  const loading = ref(initialState)
  const error = ref<string | null>(null)

  const setLoading = (state: boolean) => {
    loading.value = state
  }

  const setError = (message: string | null) => {
    error.value = message
  }

  const clearError = () => {
    error.value = null
  }

  // 包装异步函数，自动处理loading状态
  const withLoading = async <T>(asyncFn: () => Promise<T>): Promise<T | null> => {
    loading.value = true
    error.value = null
    
    try {
      const result = await asyncFn()
      return result
    } catch (err) {
      error.value = err instanceof Error ? err.message : '操作失败'
      console.error('操作失败:', err)
      return null
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    error,
    setLoading,
    setError,
    clearError,
    withLoading
  }
}
