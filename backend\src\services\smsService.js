/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 短信服务
 */

const axios = require('axios');
const SmsLog = require('../models/SmsLog');

class SmsService {
  constructor() {
    this.apiKey = process.env.YUNPIAN_API_KEY;
    this.baseUrl = 'https://sms.yunpian.com/v2/sms';
  }

  /**
   * 发送验证码短信
   */
  async sendVerificationCode(phone, code) {
    const text = `【活动报名】您的验证码是${code}，5分钟内有效，请勿泄露给他人。`;
    
    try {
      const response = await this.sendSms(phone, text, 1);
      
      // 记录短信日志
      await SmsLog.create({
        phone,
        type: 1, // 验证码
        content: text,
        status: response.success ? 1 : 2,
        yunpian_response: JSON.stringify(response)
      });
      
      return response;
    } catch (error) {
      // 记录失败日志
      await SmsLog.create({
        phone,
        type: 1,
        content: text,
        status: 2,
        yunpian_response: JSON.stringify({ error: error.message })
      });
      
      throw error;
    }
  }

  /**
   * 发送报名成功通知短信
   */
  async sendRegistrationNotification(phone, activity) {
    const text = `【活动报名】您已成功报名"${activity.title}"，活动时间：${this.formatTime(activity.start_time)}，地点：${activity.address}。请准时参加！`;
    
    try {
      const response = await this.sendSms(phone, text, 2, activity.id);
      
      // 记录短信日志
      await SmsLog.create({
        phone,
        type: 2, // 报名通知
        content: text,
        status: response.success ? 1 : 2,
        yunpian_response: JSON.stringify(response),
        event_id: activity.id
      });
      
      return response;
    } catch (error) {
      // 记录失败日志
      await SmsLog.create({
        phone,
        type: 2,
        content: text,
        status: 2,
        yunpian_response: JSON.stringify({ error: error.message }),
        event_id: activity.id
      });
      
      throw error;
    }
  }

  /**
   * 发送短信的核心方法
   */
  async sendSms(phone, text, type, eventId = null) {
    if (!this.apiKey) {
      console.warn('云片网API密钥未配置，使用模拟发送');
      return this.mockSendSms(phone, text);
    }

    try {
      const response = await axios.post(`${this.baseUrl}/single_send.json`, {
        apikey: this.apiKey,
        mobile: phone,
        text: text
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        timeout: 10000
      });

      const data = response.data;
      
      if (data.code === 0) {
        return {
          success: true,
          message: '发送成功',
          data: data
        };
      } else {
        throw new Error(data.msg || '短信发送失败');
      }
    } catch (error) {
      console.error('短信发送失败:', error.message);
      throw new Error('短信发送失败: ' + error.message);
    }
  }

  /**
   * 模拟发送短信（开发环境使用）
   */
  async mockSendSms(phone, text) {
    console.log(`[模拟短信] 发送到 ${phone}: ${text}`);
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      success: true,
      message: '模拟发送成功',
      data: {
        code: 0,
        msg: 'OK',
        count: 1,
        fee: 0.05,
        unit: 'RMB',
        mobile: phone,
        sid: Date.now()
      }
    };
  }

  /**
   * 格式化时间
   */
  formatTime(timeStr) {
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: 'numeric',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * 批量发送短信
   */
  async batchSendSms(phones, text, type, eventId = null) {
    const results = [];
    
    for (const phone of phones) {
      try {
        const result = await this.sendSms(phone, text, type, eventId);
        results.push({
          phone,
          success: true,
          result
        });
      } catch (error) {
        results.push({
          phone,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }
}

module.exports = new SmsService();
