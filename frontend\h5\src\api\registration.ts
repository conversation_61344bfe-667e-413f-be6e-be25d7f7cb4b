/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 报名相关API服务
 */

import api from './index'
import type { ApiResponse } from './index'
import type { Registration, RegistrationForm, RegistrationStatus } from '../types/registration'

/**
 * 发送验证码
 */
export const sendVerificationCode = async (phone: string, eventId: string | number): Promise<void> => {
  await api.post<ApiResponse>('/sms/send-code', {
    phone,
    eventId
  })
}

/**
 * 提交报名
 */
export const submitRegistration = async (form: RegistrationForm): Promise<Registration> => {
  const response = await api.post<ApiResponse<Registration>>('/registrations', form)
  
  if (!response.data.data) {
    throw new Error('报名提交失败')
  }
  
  return response.data.data
}

/**
 * 检查报名状态
 */
export const checkRegistrationStatus = async (
  phone: string, 
  eventId: string | number
): Promise<RegistrationStatus> => {
  const response = await api.get<ApiResponse<RegistrationStatus>>('/registrations/check', {
    params: { phone, eventId }
  })
  
  return response.data.data || { registered: false }
}

/**
 * 验证验证码
 */
export const verifyCode = async (
  phone: string, 
  code: string, 
  eventId: string | number
): Promise<boolean> => {
  try {
    await api.post<ApiResponse>('/sms/verify-code', {
      phone,
      code,
      eventId
    })
    return true
  } catch (error) {
    return false
  }
}
