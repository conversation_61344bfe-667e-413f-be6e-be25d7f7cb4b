/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 表单验证组合式函数
 */

import { ref, computed } from 'vue'
import { isValidPhone, isValidCode } from '../utils'

export function useFormValidation() {
  const errors = ref<Record<string, string>>({})

  // 验证手机号
  const validatePhone = (phone: string, fieldName = 'phone') => {
    if (!phone) {
      errors.value[fieldName] = '请输入手机号'
      return false
    }
    
    if (!isValidPhone(phone)) {
      errors.value[fieldName] = '请输入正确的手机号'
      return false
    }
    
    delete errors.value[fieldName]
    return true
  }

  // 验证验证码
  const validateCode = (code: string, fieldName = 'code') => {
    if (!code) {
      errors.value[fieldName] = '请输入验证码'
      return false
    }
    
    if (!isValidCode(code)) {
      errors.value[fieldName] = '请输入6位数字验证码'
      return false
    }
    
    delete errors.value[fieldName]
    return true
  }

  // 验证必填字段
  const validateRequired = (value: string, fieldName: string, message?: string) => {
    if (!value || value.trim() === '') {
      errors.value[fieldName] = message || `${fieldName}不能为空`
      return false
    }
    
    delete errors.value[fieldName]
    return true
  }

  // 清除错误
  const clearError = (fieldName: string) => {
    delete errors.value[fieldName]
  }

  // 清除所有错误
  const clearAllErrors = () => {
    errors.value = {}
  }

  // 检查是否有错误
  const hasErrors = computed(() => Object.keys(errors.value).length > 0)

  // 获取字段错误
  const getError = (fieldName: string) => errors.value[fieldName]

  return {
    errors,
    hasErrors,
    validatePhone,
    validateCode,
    validateRequired,
    clearError,
    clearAllErrors,
    getError
  }
}
