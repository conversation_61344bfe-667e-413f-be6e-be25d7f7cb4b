/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 倒计时组合式函数
 */

import { ref, onUnmounted } from 'vue'

export function useCountdown(initialCount = 60) {
  const count = ref(0)
  const isActive = ref(false)
  let timer: NodeJS.Timeout | null = null

  const start = (seconds = initialCount) => {
    if (isActive.value) return

    count.value = seconds
    isActive.value = true

    timer = setInterval(() => {
      count.value--
      
      if (count.value <= 0) {
        stop()
      }
    }, 1000)
  }

  const stop = () => {
    if (timer) {
      clearInterval(timer)
      timer = null
    }
    isActive.value = false
    count.value = 0
  }

  const reset = () => {
    stop()
    count.value = 0
  }

  // 组件卸载时清理定时器
  onUnmounted(() => {
    stop()
  })

  return {
    count,
    isActive,
    start,
    stop,
    reset
  }
}
