<!--
<AUTHOR>
@license http://www.178188.xyz
@lastmodify 2025年8月4日
模块说明: 错误边界组件
-->

<template>
  <div v-if="hasError" class="error-boundary">
    <van-empty
      image="error"
      :description="errorMessage"
    >
      <van-button type="primary" @click="handleRetry">
        重试
      </van-button>
    </van-empty>
  </div>
  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured } from 'vue'

interface Props {
  fallback?: string
  onError?: (error: Error) => void
}

const props = withDefaults(defineProps<Props>(), {
  fallback: '页面出现错误，请重试'
})

const emit = defineEmits<{
  retry: []
}>()

const hasError = ref(false)
const errorMessage = ref('')

onErrorCaptured((error: Error) => {
  hasError.value = true
  errorMessage.value = props.fallback
  
  console.error('ErrorBoundary caught error:', error)
  
  if (props.onError) {
    props.onError(error)
  }
  
  return false
})

const handleRetry = () => {
  hasError.value = false
  errorMessage.value = ''
  emit('retry')
}
</script>

<style scoped>
.error-boundary {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  padding: 20px;
}
</style>
