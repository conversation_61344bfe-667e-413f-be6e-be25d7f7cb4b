/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 浏览器检测工具
 */

/**
 * 检测是否为微信浏览器
 */
export const isWechat = () => {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('micromessenger')
}

/**
 * 检测是否为iOS设备
 */
export const isIOS = () => {
  const ua = navigator.userAgent.toLowerCase()
  return /iphone|ipad|ipod/.test(ua)
}

/**
 * 检测是否为Android设备
 */
export const isAndroid = () => {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('android')
}

/**
 * 检测是否为移动设备
 */
export const isMobile = () => {
  const ua = navigator.userAgent.toLowerCase()
  return /mobile|android|iphone|ipad|phone/.test(ua)
}

/**
 * 获取设备类型
 */
export const getDeviceType = () => {
  if (isIOS()) return 'ios'
  if (isAndroid()) return 'android'
  return 'desktop'
}

/**
 * 获取浏览器类型
 */
export const getBrowserType = () => {
  const ua = navigator.userAgent.toLowerCase()
  
  if (isWechat()) return 'wechat'
  if (ua.includes('chrome')) return 'chrome'
  if (ua.includes('safari')) return 'safari'
  if (ua.includes('firefox')) return 'firefox'
  
  return 'unknown'
}

/**
 * 检测是否支持触摸
 */
export const isTouchDevice = () => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

/**
 * 获取视口尺寸
 */
export const getViewportSize = () => {
  return {
    width: window.innerWidth || document.documentElement.clientWidth,
    height: window.innerHeight || document.documentElement.clientHeight
  }
}

/**
 * 检测是否为横屏
 */
export const isLandscape = () => {
  const { width, height } = getViewportSize()
  return width > height
}

/**
 * 添加浏览器类名到body
 */
export const addBrowserClass = () => {
  const body = document.body
  const deviceType = getDeviceType()
  const browserType = getBrowserType()
  
  body.classList.add(`device-${deviceType}`)
  body.classList.add(`browser-${browserType}`)
  
  if (isMobile()) {
    body.classList.add('mobile')
  }
  
  if (isTouchDevice()) {
    body.classList.add('touch')
  }
  
  if (isWechat()) {
    body.classList.add('wechat-browser')
  }
  
  if (isIOS()) {
    body.classList.add('ios-fix')
  }
}

/**
 * 监听屏幕方向变化
 */
export const onOrientationChange = (callback: (isLandscape: boolean) => void) => {
  const handleOrientationChange = () => {
    // 延迟执行，等待屏幕旋转完成
    setTimeout(() => {
      callback(isLandscape())
    }, 100)
  }
  
  window.addEventListener('orientationchange', handleOrientationChange)
  window.addEventListener('resize', handleOrientationChange)
  
  return () => {
    window.removeEventListener('orientationchange', handleOrientationChange)
    window.removeEventListener('resize', handleOrientationChange)
  }
}
