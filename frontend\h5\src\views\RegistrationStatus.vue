<!--
<AUTHOR>
@license http://www.178188.xyz
@lastmodify 2025年8月4日
模块说明: 报名状态页面
-->

<template>
  <div class="registration-status">
    <!-- 导航栏 -->
    <van-nav-bar
      title="报名状态"
      left-arrow
      @click-left="goBack"
      fixed
    />
    
    <!-- 内容区域 -->
    <div class="content">
      <van-loading v-if="loading" class="loading" />
      
      <div v-else class="status-content">
        <!-- 成功状态 -->
        <div v-if="registrationStatus?.registered" class="success-section">
          <van-result
            icon="success"
            title="报名成功"
            description="您已成功报名此活动"
          />
          
          <!-- 活动信息 -->
          <div v-if="activity" class="activity-card">
            <h3 class="activity-title">{{ activity.title }}</h3>
            <div class="activity-details">
              <div class="detail-item">
                <van-icon name="clock-o" />
                <span>{{ formatTimeRange() }}</span>
              </div>
              <div class="detail-item">
                <van-icon name="location-o" />
                <span>{{ activity.address }}</span>
              </div>
              <div class="detail-item">
                <van-icon name="friends-o" />
                <span>{{ activity.target_audience }}</span>
              </div>
            </div>
          </div>
          
          <!-- 报名信息 -->
          <div class="registration-info">
            <van-cell-group inset>
              <van-cell
                title="报名手机号"
                :value="maskedPhone"
                icon="phone-o"
              />
              <van-cell
                title="报名时间"
                :value="formatRegistrationTime()"
                icon="clock-o"
              />
              <van-cell
                title="短信状态"
                :value="getSmsStatusText()"
                icon="chat-o"
              >
                <template #right-icon>
                  <van-tag :type="getSmsStatusType()">
                    {{ getSmsStatusText() }}
                  </van-tag>
                </template>
              </van-cell>
            </van-cell-group>
          </div>
          
          <!-- 操作按钮 -->
          <div class="action-section">
            <van-button
              type="primary"
              block
              round
              @click="goToActivityDetail"
            >
              查看活动详情
            </van-button>
            
            <van-button
              type="default"
              block
              round
              class="secondary-btn"
              @click="goToActivityList"
            >
              浏览更多活动
            </van-button>
          </div>
        </div>
        
        <!-- 未报名状态 -->
        <div v-else class="not-registered-section">
          <van-result
            icon="warning"
            title="未找到报名记录"
            description="请确认手机号是否正确，或重新报名"
          />
          
          <!-- 查询表单 -->
          <div class="query-section">
            <van-cell-group inset>
              <van-field
                v-model="queryPhone"
                label="手机号"
                placeholder="请输入报名手机号"
                maxlength="11"
                type="tel"
              />
            </van-cell-group>
            
            <div class="query-actions">
              <van-button
                type="primary"
                :disabled="!isValidPhone"
                :loading="loading"
                block
                round
                @click="handleQuery"
              >
                查询报名状态
              </van-button>
              
              <van-button
                type="default"
                block
                round
                class="secondary-btn"
                @click="goToRegistration"
              >
                立即报名
              </van-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useActivityStore } from '../stores/activity'
import { useRegistrationStore } from '../stores/registration'
import type { RegistrationStatus } from '../types/registration'
import { SmsStatus } from '../types/registration'

const route = useRoute()
const router = useRouter()
const activityStore = useActivityStore()
const registrationStore = useRegistrationStore()

// 响应式数据
const loading = ref(false)
const registrationStatus = ref<RegistrationStatus | null>(null)
const queryPhone = ref('')

// 计算属性
const activity = computed(() => activityStore.currentActivity)

const maskedPhone = computed(() => {
  const phone = registrationStatus.value?.registration?.phone
  return phone ? phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : ''
})

const isValidPhone = computed(() => {
  return /^1[3-9]\d{9}$/.test(queryPhone.value)
})

// 方法
const loadData = async () => {
  const eventId = route.params.id as string
  
  loading.value = true
  
  try {
    // 加载活动详情
    await activityStore.fetchActivityDetail(eventId)
    
    // 尝试从本地存储获取手机号
    const savedPhone = localStorage.getItem('lastRegistrationPhone')
    if (savedPhone) {
      queryPhone.value = savedPhone
      await checkRegistrationStatus(savedPhone, eventId)
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    showToast('加载失败，请重试')
  } finally {
    loading.value = false
  }
}

const checkRegistrationStatus = async (phone: string, eventId: string) => {
  try {
    const status = await registrationStore.checkStatus(phone, eventId)
    registrationStatus.value = status
  } catch (error) {
    console.error('查询报名状态失败:', error)
    registrationStatus.value = { registered: false }
  }
}

const handleQuery = async () => {
  if (!isValidPhone.value) return
  
  const eventId = route.params.id as string
  loading.value = true
  
  try {
    await checkRegistrationStatus(queryPhone.value, eventId)
    
    if (registrationStatus.value?.registered) {
      // 保存手机号到本地存储
      localStorage.setItem('lastRegistrationPhone', queryPhone.value)
      showToast('查询成功')
    } else {
      showToast('未找到报名记录')
    }
  } catch (error) {
    showToast('查询失败，请重试')
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.back()
}

const goToActivityDetail = () => {
  if (activity.value) {
    router.push(`/activity/${activity.value.id}`)
  }
}

const goToActivityList = () => {
  router.push('/')
}

const goToRegistration = () => {
  if (activity.value) {
    router.push(`/registration/${activity.value.id}`)
  }
}

const formatTimeRange = () => {
  if (!activity.value) return ''
  
  const start = new Date(activity.value.start_time)
  const end = new Date(activity.value.end_time)
  
  const startStr = start.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
  
  const endStr = end.toLocaleString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
  
  return `${startStr} - ${endStr}`
}

const formatRegistrationTime = () => {
  const time = registrationStatus.value?.registration?.created_at
  if (!time) return ''
  
  return new Date(time).toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getSmsStatusText = () => {
  const status = registrationStatus.value?.registration?.sms_status
  switch (status) {
    case SmsStatus.SUCCESS:
      return '发送成功'
    case SmsStatus.FAILED:
      return '发送失败'
    case SmsStatus.PENDING:
    default:
      return '待发送'
  }
}

const getSmsStatusType = () => {
  const status = registrationStatus.value?.registration?.sms_status
  switch (status) {
    case SmsStatus.SUCCESS:
      return 'success'
    case SmsStatus.FAILED:
      return 'danger'
    case SmsStatus.PENDING:
    default:
      return 'warning'
  }
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.registration-status {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.content {
  padding-top: 46px; /* 导航栏高度 */
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.status-content {
  padding: 16px;
}

.success-section {
  text-align: center;
}

.activity-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  text-align: left;
}

.activity-title {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16px;
}

.activity-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #646566;
}

.detail-item .van-icon {
  margin-right: 8px;
  color: #969799;
}

.registration-info {
  margin: 20px 0;
}

.action-section {
  margin-top: 24px;
}

.secondary-btn {
  margin-top: 12px;
}

.not-registered-section {
  text-align: center;
}

.query-section {
  margin-top: 24px;
  text-align: left;
}

.query-actions {
  margin-top: 16px;
}
</style>
