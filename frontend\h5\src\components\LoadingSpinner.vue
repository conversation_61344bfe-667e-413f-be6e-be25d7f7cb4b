<!--
<AUTHOR>
@license http://www.178188.xyz
@lastmodify 2025年8月4日
模块说明: 加载动画组件
-->

<template>
  <div v-if="visible" class="loading-spinner" :class="{ overlay: isOverlay }">
    <van-loading
      :type="type"
      :size="size"
      :color="color"
      :text-color="textColor"
    >
      {{ text }}
    </van-loading>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  visible?: boolean
  type?: 'circular' | 'spinner'
  size?: string | number
  color?: string
  textColor?: string
  text?: string
  overlay?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  type: 'circular',
  size: '24px',
  color: '#1989fa',
  textColor: '#969799',
  text: '加载中...',
  overlay: false
})

const isOverlay = computed(() => props.overlay)
</script>

<style scoped>
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.loading-spinner.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 9999;
  backdrop-filter: blur(2px);
}
</style>
