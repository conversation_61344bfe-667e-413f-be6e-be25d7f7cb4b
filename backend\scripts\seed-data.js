/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 测试数据生成脚本
 */

const { mysqlPool } = require('../src/config/database');

async function seedData() {
  try {
    console.log('开始插入测试数据...');

    // 清空现有数据
    await mysqlPool.execute('DELETE FROM registrations');
    await mysqlPool.execute('DELETE FROM activities');
    await mysqlPool.execute('DELETE FROM sms_logs');
    
    console.log('已清空现有数据');

    // 插入测试活动数据
    const activities = [
      {
        title: '2025年春季技术交流会',
        description: '本次技术交流会将邀请行业专家分享最新的技术趋势和实践经验，包括人工智能、云计算、大数据等热门话题。欢迎所有技术爱好者参加！',
        target_audience: '技术开发者、产品经理、技术爱好者',
        address: '北京市朝阳区科技园会议中心',
        start_time: '2025-08-15 14:00:00',
        end_time: '2025-08-15 18:00:00',
        registration_end: '2025-08-14 23:59:59',
        quota: 100,
        status: 1
      },
      {
        title: '创业项目路演大赛',
        description: '为创业者提供展示项目的平台，邀请知名投资人和行业专家担任评委，优秀项目将获得投资机会和创业指导。',
        target_audience: '创业者、投资人、企业家',
        address: '上海市浦东新区创业孵化基地',
        start_time: '2025-08-20 09:00:00',
        end_time: '2025-08-20 17:00:00',
        registration_end: '2025-08-18 18:00:00',
        quota: 50,
        status: 1
      },
      {
        title: '数字化转型研讨会',
        description: '探讨企业数字化转型的策略和实践，分享成功案例，帮助企业在数字化浪潮中找到发展方向。',
        target_audience: '企业管理者、CTO、数字化转型负责人',
        address: '深圳市南山区科技大厦',
        start_time: '2025-08-25 13:30:00',
        end_time: '2025-08-25 17:30:00',
        registration_end: '2025-08-23 12:00:00',
        quota: 80,
        status: 1
      },
      {
        title: '青年职业发展论坛',
        description: '面向年轻职场人士的职业发展指导活动，邀请资深HR和职场导师分享求职技巧、职业规划等实用内容。',
        target_audience: '应届毕业生、职场新人、在校学生',
        address: '广州市天河区大学城会议厅',
        start_time: '2025-09-01 14:00:00',
        end_time: '2025-09-01 17:00:00',
        registration_end: '2025-08-30 20:00:00',
        quota: 120,
        status: 1
      },
      {
        title: '人工智能应用展示会',
        description: '展示最新的AI技术应用成果，包括机器学习、自然语言处理、计算机视觉等领域的创新项目。',
        target_audience: 'AI研究者、技术开发者、科技爱好者',
        address: '杭州市西湖区人工智能小镇',
        start_time: '2025-09-05 10:00:00',
        end_time: '2025-09-05 16:00:00',
        registration_end: '2025-09-03 18:00:00',
        quota: 200,
        status: 1
      },
      {
        title: '环保公益活动',
        description: '组织志愿者参与环保公益活动，包括垃圾分类宣传、植树造林、环保知识普及等，共同为环境保护贡献力量。',
        target_audience: '环保志愿者、公益爱好者、市民',
        address: '成都市锦江区环城公园',
        start_time: '2025-07-20 08:00:00',
        end_time: '2025-07-20 12:00:00',
        registration_end: '2025-07-18 18:00:00',
        quota: 0, // 不限人数
        status: 1
      }
    ];

    for (const activity of activities) {
      const sql = `
        INSERT INTO activities 
        (title, description, target_audience, address, start_time, end_time, registration_end, quota, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `;
      
      await mysqlPool.execute(sql, [
        activity.title,
        activity.description,
        activity.target_audience,
        activity.address,
        activity.start_time,
        activity.end_time,
        activity.registration_end,
        activity.quota,
        activity.status
      ]);
    }

    console.log(`成功插入 ${activities.length} 条活动数据`);

    // 插入一些测试报名数据
    const registrations = [
      { event_id: 1, phone: '13800138001', sms_status: 1 },
      { event_id: 1, phone: '13800138002', sms_status: 1 },
      { event_id: 1, phone: '13800138003', sms_status: 1 },
      { event_id: 2, phone: '13800138004', sms_status: 1 },
      { event_id: 2, phone: '13800138005', sms_status: 1 },
      { event_id: 3, phone: '13800138006', sms_status: 1 },
      { event_id: 4, phone: '13800138007', sms_status: 1 },
      { event_id: 4, phone: '13800138008', sms_status: 1 },
      { event_id: 4, phone: '13800138009', sms_status: 1 },
      { event_id: 4, phone: '13800138010', sms_status: 1 }
    ];

    for (const registration of registrations) {
      const sql = `
        INSERT INTO registrations 
        (event_id, phone, sms_status, created_at)
        VALUES (?, ?, ?, NOW())
      `;
      
      await mysqlPool.execute(sql, [
        registration.event_id,
        registration.phone,
        registration.sms_status
      ]);
    }

    console.log(`成功插入 ${registrations.length} 条报名数据`);
    console.log('测试数据插入完成！');

  } catch (error) {
    console.error('插入测试数据失败:', error);
  } finally {
    process.exit(0);
  }
}

// 运行脚本
seedData();
