import type { TSESLint } from '@typescript-eslint/utils';
import type { TypeOrValueSpecifier } from '../util';
export type Options = [
    {
        allowForKnownSafeCalls?: TypeOrValueSpecifier[];
        allowForKnownSafePromises?: TypeOrValueSpecifier[];
        checkThenables?: boolean;
        ignoreIIFE?: boolean;
        ignoreVoid?: boolean;
    }
];
export type MessageId = 'floating' | 'floatingFixAwait' | 'floatingFixVoid' | 'floatingPromiseArray' | 'floatingPromiseArrayVoid' | 'floatingUselessRejectionHandler' | 'floatingUselessRejectionHandlerVoid' | 'floatingVoid';
declare const _default: TSESLint.RuleModule<MessageId, Options, import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=no-floating-promises.d.ts.map