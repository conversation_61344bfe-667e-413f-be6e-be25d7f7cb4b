<!--
<AUTHOR>
@license http://www.178188.xyz
@lastmodify 2025年8月4日
模块说明: 活动列表页面
-->

<template>
  <div class="activity-list">
    <!-- 导航栏 -->
    <van-nav-bar title="活动列表" fixed />
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 搜索栏 -->
      <div class="search-section">
        <van-search
          v-model="searchKeyword"
          placeholder="搜索活动"
          @search="handleSearch"
          @clear="handleClear"
        />
      </div>

      <!-- 筛选标签 -->
      <div class="filter-section">
        <van-tabs v-model:active="activeTab" @change="handleTabChange">
          <van-tab title="全部" name="all" />
          <van-tab title="进行中" name="ongoing" />
          <van-tab title="即将开始" name="upcoming" />
        </van-tabs>
      </div>

      <!-- 活动列表 -->
      <div class="activity-list-content">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
          >
            <div
              v-for="activity in displayActivities"
              :key="activity.id"
              class="activity-card"
              @click="goToDetail(activity.id)"
            >
              <van-card>
                <template #title>
                  <div class="activity-title">{{ activity.title }}</div>
                </template>
                
                <template #desc>
                  <div class="activity-info">
                    <div class="info-row">
                      <van-icon name="location-o" />
                      <span>{{ activity.address }}</span>
                    </div>
                    <div class="info-row">
                      <van-icon name="clock-o" />
                      <span>{{ formatTime(activity.start_time) }}</span>
                    </div>
                    <div class="info-row">
                      <van-icon name="friends-o" />
                      <span>{{ activity.target_audience }}</span>
                    </div>
                  </div>
                </template>
                
                <template #footer>
                  <div class="activity-footer">
                    <div class="status-info">
                      <van-tag :type="getStatusType(activity)">
                        {{ getStatusText(activity) }}
                      </van-tag>
                      <span class="count-info">
                        {{ activity.registered_count }}/{{ activity.quota || '不限' }}人
                      </span>
                    </div>
                    <van-button
                      :type="canRegister(activity) ? 'primary' : 'default'"
                      size="small"
                      :disabled="!canRegister(activity)"
                    >
                      {{ getButtonText(activity) }}
                    </van-button>
                  </div>
                </template>
              </van-card>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useActivityStore } from '../stores/activity'
import type { Activity } from '../types/activity'
import { ActivityStatus } from '../types/activity'

const router = useRouter()
const activityStore = useActivityStore()

// 响应式数据
const searchKeyword = ref('')
const activeTab = ref('all')
const refreshing = ref(false)
const loading = ref(false)
const finished = ref(false)

// 计算属性
const displayActivities = computed(() => {
  let filtered = activityStore.publishedActivities
  
  // 根据标签筛选
  if (activeTab.value === 'ongoing') {
    const now = new Date()
    filtered = filtered.filter(activity => {
      const start = new Date(activity.start_time)
      const end = new Date(activity.end_time)
      return start <= now && now <= end
    })
  } else if (activeTab.value === 'upcoming') {
    const now = new Date()
    filtered = filtered.filter(activity => {
      const start = new Date(activity.start_time)
      return start > now
    })
  }
  
  // 根据关键词筛选
  if (searchKeyword.value) {
    filtered = filtered.filter(activity =>
      activity.title.includes(searchKeyword.value) ||
      activity.description.includes(searchKeyword.value) ||
      activity.target_audience.includes(searchKeyword.value)
    )
  }
  
  return filtered
})

// 方法
const loadActivities = async () => {
  try {
    await activityStore.fetchActivities({ status: ActivityStatus.PUBLISHED })
    finished.value = true
  } catch (error) {
    console.error('加载活动列表失败:', error)
  }
}

const onRefresh = async () => {
  refreshing.value = true
  await loadActivities()
  refreshing.value = false
}

const onLoad = async () => {
  if (!finished.value) {
    loading.value = true
    await loadActivities()
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleClear = () => {
  searchKeyword.value = ''
}

const handleTabChange = () => {
  // 标签切换逻辑已在计算属性中处理
}

const goToDetail = (id: number) => {
  router.push(`/activity/${id}`)
}

const formatTime = (timeStr: string) => {
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getStatusType = (activity: Activity) => {
  const now = new Date()
  const start = new Date(activity.start_time)
  const end = new Date(activity.end_time)
  
  if (now > end) return 'default'
  if (start <= now && now <= end) return 'success'
  return 'primary'
}

const getStatusText = (activity: Activity) => {
  const now = new Date()
  const start = new Date(activity.start_time)
  const end = new Date(activity.end_time)
  
  if (now > end) return '已结束'
  if (start <= now && now <= end) return '进行中'
  return '即将开始'
}

const canRegister = (activity: Activity) => {
  const now = new Date()
  const end = new Date(activity.end_time)
  const registrationEnd = activity.registration_end 
    ? new Date(activity.registration_end) 
    : end
  
  return (
    activity.status === ActivityStatus.PUBLISHED &&
    now <= registrationEnd &&
    now <= end &&
    (activity.quota === 0 || activity.registered_count < activity.quota)
  )
}

const getButtonText = (activity: Activity) => {
  if (!canRegister(activity)) {
    const now = new Date()
    const end = new Date(activity.end_time)
    
    if (now > end) return '已结束'
    if (activity.quota > 0 && activity.registered_count >= activity.quota) return '已满员'
    return '已截止'
  }
  return '立即报名'
}

// 生命周期
onMounted(() => {
  loadActivities()
})
</script>

<style scoped>
.activity-list {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.content {
  padding-top: 46px; /* 导航栏高度 */
}

.search-section {
  padding: 12px 16px;
  background: white;
}

.filter-section {
  background: white;
  border-bottom: 1px solid #ebedf0;
}

.activity-list-content {
  padding: 12px 16px;
}

.activity-card {
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  line-height: 1.4;
}

.activity-info {
  margin-top: 8px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 14px;
  color: #646566;
}

.info-row .van-icon {
  margin-right: 6px;
  color: #969799;
}

.activity-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.count-info {
  font-size: 12px;
  color: #969799;
}
</style>
