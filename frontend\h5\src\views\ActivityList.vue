<!--
<AUTHOR>
@license http://www.178188.xyz
@lastmodify 2025年8月4日
模块说明: 活动列表页面
-->

<template>
  <div class="activity-list-container">
    <!-- 头部导航 -->
    <header class="header">
      <div class="header-content">
        <h1 class="header-title">活动报名</h1>
      </div>
    </header>

    <!-- 筛选栏 -->
    <div class="filter-section">
      <div class="filter-buttons">
        <button
          v-for="filter in filters"
          :key="filter.value"
          @click="activeFilter = filter.value"
          :class="[
            'filter-btn',
            activeFilter === filter.value ? 'filter-btn-active' : 'filter-btn-inactive'
          ]"
        >
          {{ filter.label }}
        </button>
      </div>
    </div>

    <!-- 活动列表 -->
    <main class="main-content">
      <div v-if="loading && activities.length === 0" class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">加载中...</p>
      </div>

      <div v-else-if="displayActivities.length === 0" class="empty-container">
        <div class="empty-icon">📅</div>
        <p class="empty-text">暂无活动</p>
      </div>

      <div v-else>
        <!-- 活动卡片 -->
        <div
          v-for="activity in displayActivities"
          :key="activity.id"
          class="activity-card"
          @click="goToDetail(activity.id)"
        >
          <div class="card-content">
            <div class="card-header">
              <h3 class="card-title">
                {{ activity.title }}
              </h3>
              <span :class="getStatusClass(activity)" class="status-badge">
                {{ getStatusText(activity) }}
              </span>
            </div>

            <div class="card-info">
              <div class="info-item">
                <span class="info-icon">📅</span>
                <span>{{ formatTime(activity.start_time) }}</span>
              </div>
              <div class="info-item">
                <span class="info-icon">📍</span>
                <span>{{ activity.address }}</span>
              </div>
              <div class="info-item">
                <span class="info-icon">👥</span>
                <span>{{ activity.target_audience }}</span>
              </div>
            </div>

            <div class="card-footer">
              <div class="registration-count">
                <span>已报名 <span :class="getCountClass(activity)" class="count-number">{{ activity.registered_count }}</span>/{{ activity.quota || '不限' }}人</span>
              </div>
              <button
                :class="getButtonClass(activity)"
                class="register-btn"
                @click.stop="handleRegister(activity)"
              >
                {{ getButtonText(activity) }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 加载更多 -->
    <div v-if="!loading && displayActivities.length > 0" class="px-4 py-6 text-center">
      <button class="text-gray-500 text-sm" @click="loadMore">加载更多活动...</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useActivityStore } from '../stores/activity'
import type { Activity } from '../types/activity'
import { ActivityStatus } from '../types/activity'

const router = useRouter()
const activityStore = useActivityStore()

// 响应式数据
const activeFilter = ref('all')
const loading = ref(false)

// 筛选选项
const filters = [
  { label: '全部', value: 'all' },
  { label: '报名中', value: 'ongoing' },
  { label: '即将开始', value: 'upcoming' },
  { label: '已结束', value: 'ended' }
]

// 计算属性
const activities = computed(() => activityStore.activities)

const displayActivities = computed(() => {
  let filtered = activities.value.filter(activity => activity.status === ActivityStatus.PUBLISHED)

  const now = new Date()

  // 根据筛选条件过滤
  if (activeFilter.value === 'ongoing') {
    filtered = filtered.filter(activity => {
      const start = new Date(activity.start_time)
      const end = new Date(activity.end_time)
      const regEnd = activity.registration_end ? new Date(activity.registration_end) : end
      return start <= now && now <= regEnd && (activity.quota === 0 || activity.registered_count < activity.quota)
    })
  } else if (activeFilter.value === 'upcoming') {
    filtered = filtered.filter(activity => {
      const start = new Date(activity.start_time)
      return start > now
    })
  } else if (activeFilter.value === 'ended') {
    filtered = filtered.filter(activity => {
      const end = new Date(activity.end_time)
      return now > end
    })
  }

  return filtered
})

// 方法
const loadActivities = async () => {
  loading.value = true
  try {
    await activityStore.fetchActivities({ status: ActivityStatus.PUBLISHED })
  } catch (error) {
    console.error('加载活动列表失败:', error)
  } finally {
    loading.value = false
  }
}

const loadMore = () => {
  // 这里可以实现分页加载更多
  console.log('加载更多')
}

const goToDetail = (id: number) => {
  router.push(`/activity/${id}`)
}

const handleRegister = (activity: Activity) => {
  if (canRegister(activity)) {
    router.push(`/registration/${activity.id}`)
  }
}

const formatTime = (timeStr: string) => {
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getStatusText = (activity: Activity) => {
  const now = new Date()
  const start = new Date(activity.start_time)
  const end = new Date(activity.end_time)
  const regEnd = activity.registration_end ? new Date(activity.registration_end) : end

  if (now > end) return '已结束'
  if (activity.quota > 0 && activity.registered_count >= activity.quota) return '已满员'
  if (now > regEnd) return '报名截止'
  if (start <= now && now <= regEnd) return '报名中'
  return '即将开始'
}

const getStatusClass = (activity: Activity) => {
  const status = getStatusText(activity)

  switch (status) {
    case '报名中':
      return 'status-ongoing'
    case '已满员':
      return 'status-full'
    case '已结束':
    case '报名截止':
      return 'status-ended'
    default:
      return 'status-upcoming'
  }
}

const canRegister = (activity: Activity) => {
  const now = new Date()
  const end = new Date(activity.end_time)
  const registrationEnd = activity.registration_end
    ? new Date(activity.registration_end)
    : end

  return (
    activity.status === ActivityStatus.PUBLISHED &&
    now <= registrationEnd &&
    now <= end &&
    (activity.quota === 0 || activity.registered_count < activity.quota)
  )
}

const getButtonText = (activity: Activity) => {
  if (!canRegister(activity)) {
    const now = new Date()
    const end = new Date(activity.end_time)

    if (now > end) return '已结束'
    if (activity.quota > 0 && activity.registered_count >= activity.quota) return '已满员'
    return '已截止'
  }
  return '立即报名'
}

const getButtonClass = (activity: Activity) => {
  if (canRegister(activity)) {
    return 'btn-primary'
  }
  return 'btn-disabled'
}

const getCountClass = (activity: Activity) => {
  if (activity.quota > 0) {
    const ratio = activity.registered_count / activity.quota
    if (ratio >= 1) return 'count-danger'
    if (ratio >= 0.8) return 'count-warning'
    return 'count-normal'
  }
  return 'count-normal'
}

// 生命周期
onMounted(() => {
  loadActivities()
})
</script>

<style scoped>
/* 容器样式 */
.activity-list-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

/* 头部样式 */
.header {
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-content {
  padding: 12px 16px;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

/* 筛选栏样式 */
.filter-section {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 12px 16px;
}

.filter-buttons {
  display: flex;
  gap: 8px;
  overflow-x: auto;
}

.filter-buttons::-webkit-scrollbar {
  display: none;
}

.filter-btn {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.filter-btn-active {
  background-color: #3b82f6;
  color: white;
}

.filter-btn-inactive {
  background-color: #f3f4f6;
  color: #6b7280;
}

.filter-btn-inactive:hover {
  background-color: #e5e7eb;
}

/* 主内容样式 */
.main-content {
  padding: 16px;
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 32px;
}

.loading-spinner {
  display: inline-block;
  width: 32px;
  height: 32px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 8px;
  color: #6b7280;
}

/* 空状态 */
.empty-container {
  text-align: center;
  padding: 48px 16px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  color: #6b7280;
}

/* 活动卡片样式 */
.activity-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.activity-card:active {
  transform: scale(0.98);
}

.card-content {
  padding: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
  flex: 1;
  margin-right: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.card-info {
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #6b7280;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  margin-right: 8px;
  font-size: 16px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.registration-count {
  font-size: 14px;
  color: #6b7280;
}

.count-number {
  font-weight: 500;
}

.register-btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* 状态样式 */
.status-ongoing {
  background-color: #dcfce7;
  color: #16a34a;
}

.status-full {
  background-color: #fee2e2;
  color: #dc2626;
}

.status-ended {
  background-color: #f3f4f6;
  color: #6b7280;
}

.status-upcoming {
  background-color: #dbeafe;
  color: #2563eb;
}

/* 按钮样式 */
.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-disabled {
  background-color: #d1d5db;
  color: #6b7280;
  cursor: not-allowed;
}

/* 计数样式 */
.count-normal {
  color: #2563eb;
}

.count-warning {
  color: #ea580c;
}

.count-danger {
  color: #dc2626;
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
