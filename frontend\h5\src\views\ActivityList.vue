<!--
<AUTHOR>
@license http://www.178188.xyz
@lastmodify 2025年8月4日
模块说明: 活动列表页面
-->

<template>
  <div class="bg-gray-50 min-h-screen">
    <!-- 头部导航 -->
    <header class="bg-white shadow-sm sticky top-0 z-10">
      <div class="px-4 py-3">
        <h1 class="text-lg font-semibold text-gray-900">活动报名</h1>
      </div>
    </header>

    <!-- 筛选栏 -->
    <div class="bg-white border-b border-gray-200 px-4 py-3">
      <div class="flex space-x-2 overflow-x-auto">
        <button
          v-for="filter in filters"
          :key="filter.value"
          @click="activeFilter = filter.value"
          :class="[
            'px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors',
            activeFilter === filter.value
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
          ]"
        >
          {{ filter.label }}
        </button>
      </div>
    </div>

    <!-- 活动列表 -->
    <main class="px-4 py-4 space-y-4">
      <div v-if="loading && activities.length === 0" class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-500">加载中...</p>
      </div>

      <div v-else-if="displayActivities.length === 0" class="text-center py-12">
        <div class="text-gray-400 text-6xl mb-4">📅</div>
        <p class="text-gray-500">暂无活动</p>
      </div>

      <div v-else>
        <!-- 活动卡片 -->
        <div
          v-for="activity in displayActivities"
          :key="activity.id"
          :class="[
            'activity-card bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden transition-all duration-300',
            !canRegister(activity) ? 'opacity-75' : ''
          ]"
          @click="goToDetail(activity.id)"
        >
          <div class="p-4">
            <div class="flex justify-between items-start mb-2">
              <h3 class="text-lg font-semibold text-gray-900 line-clamp-2 flex-1 mr-2">
                {{ activity.title }}
              </h3>
              <span :class="getStatusClass(activity)" class="px-2 py-1 rounded-full text-xs font-medium ml-2 whitespace-nowrap">
                {{ getStatusText(activity) }}
              </span>
            </div>

            <div class="space-y-2 text-sm text-gray-600 mb-3">
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <span>{{ formatTime(activity.start_time) }}</span>
              </div>
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span>{{ activity.address }}</span>
              </div>
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span>{{ activity.target_audience }}</span>
              </div>
            </div>

            <div class="flex justify-between items-center">
              <div class="text-sm text-gray-500">
                <span>已报名 <span :class="getCountClass(activity)" class="font-medium">{{ activity.registered_count }}</span>/{{ activity.quota || '不限' }}人</span>
              </div>
              <button
                :class="getButtonClass(activity)"
                class="px-4 py-2 rounded-lg text-sm font-medium"
                :disabled="!canRegister(activity)"
                @click.stop="handleRegister(activity)"
              >
                {{ getButtonText(activity) }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- 加载更多 -->
    <div class="px-4 py-6 text-center">
      <button class="text-gray-500 text-sm" @click="loadMore">加载更多活动...</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useActivityStore } from '../stores/activity'
import type { Activity } from '../types/activity'
import { ActivityStatus } from '../types/activity'

const router = useRouter()
const activityStore = useActivityStore()

// 响应式数据
const activeFilter = ref('all')
const loading = ref(false)

// 筛选选项
const filters = [
  { label: '全部', value: 'all' },
  { label: '报名中', value: 'ongoing' },
  { label: '即将开始', value: 'upcoming' },
  { label: '已结束', value: 'ended' }
]

// 计算属性
const activities = computed(() => activityStore.activities)

const displayActivities = computed(() => {
  let filtered = activities.value.filter(activity => activity.status === ActivityStatus.PUBLISHED)

  const now = new Date()

  // 根据筛选条件过滤
  if (activeFilter.value === 'ongoing') {
    filtered = filtered.filter(activity => {
      const start = new Date(activity.start_time)
      const end = new Date(activity.end_time)
      const regEnd = activity.registration_end ? new Date(activity.registration_end) : end
      return start <= now && now <= regEnd && (activity.quota === 0 || activity.registered_count < activity.quota)
    })
  } else if (activeFilter.value === 'upcoming') {
    filtered = filtered.filter(activity => {
      const start = new Date(activity.start_time)
      return start > now
    })
  } else if (activeFilter.value === 'ended') {
    filtered = filtered.filter(activity => {
      const end = new Date(activity.end_time)
      return now > end
    })
  }

  return filtered
})

// 方法
const loadActivities = async () => {
  loading.value = true
  try {
    await activityStore.fetchActivities({ status: ActivityStatus.PUBLISHED })
  } catch (error) {
    console.error('加载活动列表失败:', error)
  } finally {
    loading.value = false
  }
}

const loadMore = () => {
  // 这里可以实现分页加载更多
  console.log('加载更多')
}

const goToDetail = (id: number) => {
  router.push(`/activity/${id}`)
}

const handleRegister = (activity: Activity) => {
  if (canRegister(activity)) {
    router.push(`/registration/${activity.id}`)
  }
}

const formatTime = (timeStr: string) => {
  const date = new Date(timeStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getStatusText = (activity: Activity) => {
  const now = new Date()
  const start = new Date(activity.start_time)
  const end = new Date(activity.end_time)
  const regEnd = activity.registration_end ? new Date(activity.registration_end) : end

  if (now > end) return '已结束'
  if (activity.quota > 0 && activity.registered_count >= activity.quota) return '已满员'
  if (now > regEnd) return '报名截止'
  if (start <= now && now <= regEnd) return '报名中'
  return '即将开始'
}

const getStatusClass = (activity: Activity) => {
  const status = getStatusText(activity)

  switch (status) {
    case '报名中':
      return 'bg-green-100 text-green-600'
    case '已满员':
      return 'bg-red-100 text-red-600'
    case '已结束':
    case '报名截止':
      return 'bg-gray-100 text-gray-500'
    default:
      return 'bg-blue-100 text-blue-600'
  }
}

const canRegister = (activity: Activity) => {
  const now = new Date()
  const end = new Date(activity.end_time)
  const registrationEnd = activity.registration_end
    ? new Date(activity.registration_end)
    : end

  return (
    activity.status === ActivityStatus.PUBLISHED &&
    now <= registrationEnd &&
    now <= end &&
    (activity.quota === 0 || activity.registered_count < activity.quota)
  )
}

const getButtonText = (activity: Activity) => {
  if (!canRegister(activity)) {
    const now = new Date()
    const end = new Date(activity.end_time)

    if (now > end) return '已结束'
    if (activity.quota > 0 && activity.registered_count >= activity.quota) return '已满员'
    return '已截止'
  }
  return '立即报名'
}

const getButtonClass = (activity: Activity) => {
  if (canRegister(activity)) {
    return 'bg-blue-500 text-white hover:bg-blue-600 active:bg-blue-700'
  }
  return 'bg-gray-300 text-gray-500 cursor-not-allowed'
}

const getCountClass = (activity: Activity) => {
  if (activity.quota > 0) {
    const ratio = activity.registered_count / activity.quota
    if (ratio >= 1) return 'text-red-600'
    if (ratio >= 0.8) return 'text-orange-600'
    return 'text-blue-600'
  }
  return 'text-blue-600'
}

// 生命周期
onMounted(() => {
  loadActivities()
})
</script>

<style scoped>
/* 微信浏览器兼容性 */
body {
  -webkit-text-size-adjust: 100%;
}

.activity-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.activity-card:active {
  transform: scale(0.98);
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 自定义滚动条 */
.overflow-x-auto::-webkit-scrollbar {
  display: none;
}

.overflow-x-auto {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
