<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>活动报名系统 - 测试页面</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .activity-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .activity-card:active {
            transform: scale(0.98);
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="min-h-screen bg-gray-50">
            <!-- 头部导航 -->
            <header class="bg-white shadow-sm sticky top-0 z-10">
                <div class="px-4 py-3">
                    <h1 class="text-lg font-semibold text-gray-900">活动报名</h1>
                </div>
            </header>

            <!-- 筛选栏 -->
            <div class="bg-white border-b border-gray-200 px-4 py-3">
                <div class="flex space-x-2 overflow-x-auto">
                    <button 
                        v-for="filter in filters" 
                        :key="filter.value"
                        @click="activeFilter = filter.value"
                        :class="[
                            'px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-colors',
                            activeFilter === filter.value 
                                ? 'bg-blue-500 text-white' 
                                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                        ]"
                    >
                        {{ filter.label }}
                    </button>
                </div>
            </div>

            <!-- 活动列表 -->
            <main class="px-4 py-4 space-y-4">
                <div v-if="loading" class="text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-500">加载中...</p>
                </div>

                <div v-else-if="error" class="text-center py-12">
                    <div class="text-red-400 text-6xl mb-4">❌</div>
                    <p class="text-red-500">{{ error }}</p>
                    <button @click="loadActivities" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg">重试</button>
                </div>

                <div v-else-if="displayActivities.length === 0" class="text-center py-12">
                    <div class="text-gray-400 text-6xl mb-4">📅</div>
                    <p class="text-gray-500">暂无活动</p>
                </div>

                <div v-else>
                    <!-- 活动卡片 -->
                    <div
                        v-for="activity in displayActivities"
                        :key="activity.id"
                        class="activity-card bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
                        @click="goToDetail(activity.id)"
                    >
                        <div class="p-4">
                            <div class="flex justify-between items-start mb-2">
                                <h3 class="text-lg font-semibold text-gray-900 flex-1 mr-2">
                                    {{ activity.title }}
                                </h3>
                                <span :class="getStatusClass(activity)" class="px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap">
                                    {{ getStatusText(activity) }}
                                </span>
                            </div>
                            
                            <div class="space-y-2 text-sm text-gray-600 mb-3">
                                <div class="flex items-center">
                                    <span class="mr-2">📅</span>
                                    <span>{{ formatTime(activity.start_time) }}</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="mr-2">📍</span>
                                    <span>{{ activity.address }}</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="mr-2">👥</span>
                                    <span>{{ activity.target_audience }}</span>
                                </div>
                            </div>
                            
                            <div class="flex justify-between items-center">
                                <div class="text-sm text-gray-500">
                                    <span>已报名 <span :class="getCountClass(activity)" class="font-medium">{{ activity.registered_count }}</span>/{{ activity.quota || '不限' }}人</span>
                                </div>
                                <button 
                                    :class="getButtonClass(activity)"
                                    class="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                                    @click.stop="handleRegister(activity)"
                                >
                                    {{ getButtonText(activity) }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        const { createApp, ref, computed, onMounted } = Vue;

        createApp({
            setup() {
                // 响应式数据
                const activities = ref([]);
                const loading = ref(false);
                const error = ref(null);
                const activeFilter = ref('all');

                // 筛选选项
                const filters = [
                    { label: '全部', value: 'all' },
                    { label: '报名中', value: 'ongoing' },
                    { label: '即将开始', value: 'upcoming' },
                    { label: '已结束', value: 'ended' }
                ];

                // 计算属性
                const displayActivities = computed(() => {
                    let filtered = activities.value.filter(activity => activity.status === 1);
                    
                    const now = new Date();
                    
                    if (activeFilter.value === 'ongoing') {
                        filtered = filtered.filter(activity => {
                            const start = new Date(activity.start_time);
                            const end = new Date(activity.end_time);
                            const regEnd = activity.registration_end ? new Date(activity.registration_end) : end;
                            return start <= now && now <= regEnd && (activity.quota === 0 || activity.registered_count < activity.quota);
                        });
                    } else if (activeFilter.value === 'upcoming') {
                        filtered = filtered.filter(activity => {
                            const start = new Date(activity.start_time);
                            return start > now;
                        });
                    } else if (activeFilter.value === 'ended') {
                        filtered = filtered.filter(activity => {
                            const end = new Date(activity.end_time);
                            return now > end;
                        });
                    }
                    
                    return filtered;
                });

                // 方法
                const loadActivities = async () => {
                    loading.value = true;
                    error.value = null;
                    
                    try {
                        const response = await axios.get('http://localhost:3000/api/activities');
                        if (response.data.success) {
                            activities.value = response.data.data.data || [];
                        } else {
                            throw new Error(response.data.message || '获取活动列表失败');
                        }
                    } catch (err) {
                        error.value = err.message || '网络错误';
                        console.error('加载活动列表失败:', err);
                    } finally {
                        loading.value = false;
                    }
                };

                const formatTime = (timeStr) => {
                    const date = new Date(timeStr);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: 'numeric',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                };

                const getStatusText = (activity) => {
                    const now = new Date();
                    const start = new Date(activity.start_time);
                    const end = new Date(activity.end_time);
                    const regEnd = activity.registration_end ? new Date(activity.registration_end) : end;
                    
                    if (now > end) return '已结束';
                    if (activity.quota > 0 && activity.registered_count >= activity.quota) return '已满员';
                    if (now > regEnd) return '报名截止';
                    if (start <= now && now <= regEnd) return '报名中';
                    return '即将开始';
                };

                const getStatusClass = (activity) => {
                    const status = getStatusText(activity);
                    
                    switch (status) {
                        case '报名中':
                            return 'bg-green-100 text-green-600';
                        case '已满员':
                            return 'bg-red-100 text-red-600';
                        case '已结束':
                        case '报名截止':
                            return 'bg-gray-100 text-gray-500';
                        default:
                            return 'bg-blue-100 text-blue-600';
                    }
                };

                const canRegister = (activity) => {
                    const now = new Date();
                    const end = new Date(activity.end_time);
                    const registrationEnd = activity.registration_end 
                        ? new Date(activity.registration_end) 
                        : end;
                    
                    return (
                        activity.status === 1 &&
                        now <= registrationEnd &&
                        now <= end &&
                        (activity.quota === 0 || activity.registered_count < activity.quota)
                    );
                };

                const getButtonText = (activity) => {
                    if (!canRegister(activity)) {
                        const now = new Date();
                        const end = new Date(activity.end_time);
                        
                        if (now > end) return '已结束';
                        if (activity.quota > 0 && activity.registered_count >= activity.quota) return '已满员';
                        return '已截止';
                    }
                    return '立即报名';
                };

                const getButtonClass = (activity) => {
                    if (canRegister(activity)) {
                        return 'bg-blue-500 text-white hover:bg-blue-600';
                    }
                    return 'bg-gray-300 text-gray-500 cursor-not-allowed';
                };

                const getCountClass = (activity) => {
                    if (activity.quota > 0) {
                        const ratio = activity.registered_count / activity.quota;
                        if (ratio >= 1) return 'text-red-600';
                        if (ratio >= 0.8) return 'text-orange-600';
                        return 'text-blue-600';
                    }
                    return 'text-blue-600';
                };

                const goToDetail = (id) => {
                    alert(`跳转到活动详情页: ${id}`);
                };

                const handleRegister = (activity) => {
                    if (canRegister(activity)) {
                        alert(`跳转到报名页面: ${activity.id}`);
                    }
                };

                // 生命周期
                onMounted(() => {
                    loadActivities();
                });

                return {
                    activities,
                    loading,
                    error,
                    activeFilter,
                    filters,
                    displayActivities,
                    loadActivities,
                    formatTime,
                    getStatusText,
                    getStatusClass,
                    getButtonText,
                    getButtonClass,
                    getCountClass,
                    goToDetail,
                    handleRegister
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
