/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 工具函数集合
 */

/**
 * 格式化时间
 */
export const formatTime = (timeStr: string, format: 'datetime' | 'date' | 'time' = 'datetime') => {
  const date = new Date(timeStr)
  
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric'
  }
  
  if (format === 'datetime' || format === 'time') {
    options.hour = '2-digit'
    options.minute = '2-digit'
  }
  
  return date.toLocaleString('zh-CN', options)
}

/**
 * 格式化时间范围
 */
export const formatTimeRange = (startTime: string, endTime: string) => {
  const start = new Date(startTime)
  const end = new Date(endTime)
  
  const startStr = start.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
  
  // 如果是同一天，只显示结束时间
  if (start.toDateString() === end.toDateString()) {
    const endStr = end.toLocaleString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
    return `${startStr} - ${endStr}`
  } else {
    const endStr = end.toLocaleString('zh-CN', {
      year: 'numeric',
      month: 'numeric',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
    return `${startStr} - ${endStr}`
  }
}

/**
 * 手机号脱敏
 */
export const maskPhone = (phone: string) => {
  if (!phone || phone.length !== 11) return phone
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 验证手机号
 */
export const isValidPhone = (phone: string) => {
  return /^1[3-9]\d{9}$/.test(phone)
}

/**
 * 验证验证码
 */
export const isValidCode = (code: string) => {
  return /^\d{6}$/.test(code)
}

/**
 * 防抖函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * 节流函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let lastTime = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastTime >= wait) {
      lastTime = now
      func(...args)
    }
  }
}

/**
 * 获取活动状态文本
 */
export const getActivityStatusText = (startTime: string, endTime: string) => {
  const now = new Date()
  const start = new Date(startTime)
  const end = new Date(endTime)
  
  if (now > end) return '已结束'
  if (start <= now && now <= end) return '进行中'
  return '即将开始'
}

/**
 * 获取活动状态类型
 */
export const getActivityStatusType = (startTime: string, endTime: string) => {
  const now = new Date()
  const start = new Date(startTime)
  const end = new Date(endTime)
  
  if (now > end) return 'default'
  if (start <= now && now <= end) return 'success'
  return 'primary'
}

/**
 * 检查活动是否可以报名
 */
export const canRegisterActivity = (
  status: number,
  startTime: string,
  endTime: string,
  registrationEnd: string | null,
  quota: number,
  registeredCount: number
) => {
  const now = new Date()
  const end = new Date(endTime)
  const regEnd = registrationEnd ? new Date(registrationEnd) : end
  
  return (
    status === 1 && // 已发布
    now <= regEnd && // 报名未截止
    now <= end && // 活动未结束
    (quota === 0 || registeredCount < quota) // 未满员
  )
}

/**
 * 本地存储工具
 */
export const storage = {
  set(key: string, value: any) {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('存储失败:', error)
    }
  },
  
  get<T = any>(key: string, defaultValue?: T): T | null {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue || null
    } catch (error) {
      console.error('读取存储失败:', error)
      return defaultValue || null
    }
  },
  
  remove(key: string) {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('删除存储失败:', error)
    }
  },
  
  clear() {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('清空存储失败:', error)
    }
  }
}

/**
 * URL参数解析
 */
export const parseQuery = (search: string) => {
  const params = new URLSearchParams(search)
  const result: Record<string, string> = {}
  
  for (const [key, value] of params) {
    result[key] = value
  }
  
  return result
}

/**
 * 复制到剪贴板
 */
export const copyToClipboard = async (text: string) => {
  try {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(text)
    } else {
      // 兼容旧浏览器
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
    }
    return true
  } catch (error) {
    console.error('复制失败:', error)
    return false
  }
}
