/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: H5用户端路由配置
 */

import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'ActivityList',
      component: () => import('../views/ActivityList.vue'),
      meta: {
        title: '活动列表'
      }
    },
    {
      path: '/activity/:id',
      name: 'ActivityDetail',
      component: () => import('../views/ActivityDetail.vue'),
      meta: {
        title: '活动详情'
      }
    },
    {
      path: '/registration/:id',
      name: 'Registration',
      component: () => import('../views/Registration.vue'),
      meta: {
        title: '活动报名'
      }
    },
    {
      path: '/registration-status/:id',
      name: 'RegistrationStatus',
      component: () => import('../views/RegistrationStatus.vue'),
      meta: {
        title: '报名状态'
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      redirect: '/'
    }
  ]
})

// 路由守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 活动报名系统`
  }
  next()
})

export default router
