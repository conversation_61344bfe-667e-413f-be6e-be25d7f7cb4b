import { type ExtractPropTypes } from 'vue';
import type { SearchShape } from './types';
export declare const searchProps: {
    id: StringConstructor;
    name: StringConstructor;
    leftIcon: StringConstructor;
    rightIcon: StringConstructor;
    autofocus: BooleanConstructor;
    clearable: BooleanConstructor;
    maxlength: (NumberConstructor | StringConstructor)[];
    max: NumberConstructor;
    min: NumberConstructor;
    formatter: import("vue").PropType<(value: string) => string>;
    clearIcon: {
        type: import("vue").PropType<string>;
        default: string;
    };
    modelValue: {
        type: (NumberConstructor | StringConstructor)[];
        default: string;
    };
    inputAlign: import("vue").PropType<import("../field").FieldTextAlign>;
    placeholder: StringConstructor;
    autocomplete: StringConstructor;
    autocapitalize: StringConstructor;
    autocorrect: StringConstructor;
    errorMessage: StringConstructor;
    enterkeyhint: StringConstructor;
    clearTrigger: {
        type: import("vue").PropType<import("../field").FieldClearTrigger>;
        default: import("../field").FieldClearTrigger;
    };
    formatTrigger: {
        type: import("vue").PropType<import("../field").FieldFormatTrigger>;
        default: import("../field").FieldFormatTrigger;
    };
    spellcheck: {
        type: BooleanConstructor;
        default: null;
    };
    error: {
        type: BooleanConstructor;
        default: null;
    };
    disabled: {
        type: BooleanConstructor;
        default: null;
    };
    readonly: {
        type: BooleanConstructor;
        default: null;
    };
    inputmode: import("vue").PropType<import("vue").HTMLAttributes["inputmode"]>;
} & {
    label: StringConstructor;
    shape: {
        type: import("vue").PropType<SearchShape>;
        default: SearchShape;
    };
    leftIcon: {
        type: import("vue").PropType<string>;
        default: string;
    };
    clearable: {
        type: BooleanConstructor;
        default: true;
    };
    actionText: StringConstructor;
    background: StringConstructor;
    showAction: BooleanConstructor;
};
export type SearchProps = ExtractPropTypes<typeof searchProps>;
declare const _default: import("vue").DefineComponent<ExtractPropTypes<{
    id: StringConstructor;
    name: StringConstructor;
    leftIcon: StringConstructor;
    rightIcon: StringConstructor;
    autofocus: BooleanConstructor;
    clearable: BooleanConstructor;
    maxlength: (NumberConstructor | StringConstructor)[];
    max: NumberConstructor;
    min: NumberConstructor;
    formatter: import("vue").PropType<(value: string) => string>;
    clearIcon: {
        type: import("vue").PropType<string>;
        default: string;
    };
    modelValue: {
        type: (NumberConstructor | StringConstructor)[];
        default: string;
    };
    inputAlign: import("vue").PropType<import("../field").FieldTextAlign>;
    placeholder: StringConstructor;
    autocomplete: StringConstructor;
    autocapitalize: StringConstructor;
    autocorrect: StringConstructor;
    errorMessage: StringConstructor;
    enterkeyhint: StringConstructor;
    clearTrigger: {
        type: import("vue").PropType<import("../field").FieldClearTrigger>;
        default: import("../field").FieldClearTrigger;
    };
    formatTrigger: {
        type: import("vue").PropType<import("../field").FieldFormatTrigger>;
        default: import("../field").FieldFormatTrigger;
    };
    spellcheck: {
        type: BooleanConstructor;
        default: null;
    };
    error: {
        type: BooleanConstructor;
        default: null;
    };
    disabled: {
        type: BooleanConstructor;
        default: null;
    };
    readonly: {
        type: BooleanConstructor;
        default: null;
    };
    inputmode: import("vue").PropType<import("vue").HTMLAttributes["inputmode"]>;
} & {
    label: StringConstructor;
    shape: {
        type: import("vue").PropType<SearchShape>;
        default: SearchShape;
    };
    leftIcon: {
        type: import("vue").PropType<string>;
        default: string;
    };
    clearable: {
        type: BooleanConstructor;
        default: true;
    };
    actionText: StringConstructor;
    background: StringConstructor;
    showAction: BooleanConstructor;
}>, () => import("vue/jsx-runtime").JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("search" | "clear" | "focus" | "blur" | "clickInput" | "clickLeftIcon" | "clickRightIcon" | "update:modelValue" | "cancel")[], "search" | "clear" | "focus" | "blur" | "clickInput" | "clickLeftIcon" | "clickRightIcon" | "update:modelValue" | "cancel", import("vue").PublicProps, Readonly<ExtractPropTypes<{
    id: StringConstructor;
    name: StringConstructor;
    leftIcon: StringConstructor;
    rightIcon: StringConstructor;
    autofocus: BooleanConstructor;
    clearable: BooleanConstructor;
    maxlength: (NumberConstructor | StringConstructor)[];
    max: NumberConstructor;
    min: NumberConstructor;
    formatter: import("vue").PropType<(value: string) => string>;
    clearIcon: {
        type: import("vue").PropType<string>;
        default: string;
    };
    modelValue: {
        type: (NumberConstructor | StringConstructor)[];
        default: string;
    };
    inputAlign: import("vue").PropType<import("../field").FieldTextAlign>;
    placeholder: StringConstructor;
    autocomplete: StringConstructor;
    autocapitalize: StringConstructor;
    autocorrect: StringConstructor;
    errorMessage: StringConstructor;
    enterkeyhint: StringConstructor;
    clearTrigger: {
        type: import("vue").PropType<import("../field").FieldClearTrigger>;
        default: import("../field").FieldClearTrigger;
    };
    formatTrigger: {
        type: import("vue").PropType<import("../field").FieldFormatTrigger>;
        default: import("../field").FieldFormatTrigger;
    };
    spellcheck: {
        type: BooleanConstructor;
        default: null;
    };
    error: {
        type: BooleanConstructor;
        default: null;
    };
    disabled: {
        type: BooleanConstructor;
        default: null;
    };
    readonly: {
        type: BooleanConstructor;
        default: null;
    };
    inputmode: import("vue").PropType<import("vue").HTMLAttributes["inputmode"]>;
} & {
    label: StringConstructor;
    shape: {
        type: import("vue").PropType<SearchShape>;
        default: SearchShape;
    };
    leftIcon: {
        type: import("vue").PropType<string>;
        default: string;
    };
    clearable: {
        type: BooleanConstructor;
        default: true;
    };
    actionText: StringConstructor;
    background: StringConstructor;
    showAction: BooleanConstructor;
}>> & Readonly<{
    onFocus?: ((...args: any[]) => any) | undefined;
    onBlur?: ((...args: any[]) => any) | undefined;
    onClear?: ((...args: any[]) => any) | undefined;
    onClickInput?: ((...args: any[]) => any) | undefined;
    onClickLeftIcon?: ((...args: any[]) => any) | undefined;
    onClickRightIcon?: ((...args: any[]) => any) | undefined;
    "onUpdate:modelValue"?: ((...args: any[]) => any) | undefined;
    onCancel?: ((...args: any[]) => any) | undefined;
    onSearch?: ((...args: any[]) => any) | undefined;
}>, {
    autofocus: boolean;
    disabled: boolean;
    shape: SearchShape;
    leftIcon: string;
    clearable: boolean;
    clearIcon: string;
    modelValue: string | number;
    clearTrigger: import("../field").FieldClearTrigger;
    formatTrigger: import("../field").FieldFormatTrigger;
    spellcheck: boolean;
    error: boolean;
    readonly: boolean;
    showAction: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
