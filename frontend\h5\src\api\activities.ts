/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 活动相关API服务
 */

import api from './index'
import type { ApiResponse } from './index'
import type { Activity, ActivityListParams } from '../types/activity'

/**
 * 获取活动列表
 */
export const getActivities = async (params?: ActivityListParams): Promise<Activity[]> => {
  const response = await api.get<ApiResponse<{
    data: Activity[]
    total: number
    page: number
    limit: number
  }>>('/activities', { params })
  
  return response.data.data?.data || []
}

/**
 * 获取活动详情
 */
export const getActivityDetail = async (id: string | number): Promise<Activity> => {
  const response = await api.get<ApiResponse<Activity>>(`/activities/${id}`)
  
  if (!response.data.data) {
    throw new Error('活动不存在')
  }
  
  return response.data.data
}

/**
 * 检查活动状态
 */
export const checkActivityStatus = async (id: string | number): Promise<{
  canRegister: boolean
  reason?: string
}> => {
  try {
    const activity = await getActivityDetail(id)
    const now = new Date()
    const startTime = new Date(activity.start_time)
    const endTime = new Date(activity.end_time)
    const registrationEnd = activity.registration_end ? new Date(activity.registration_end) : endTime
    
    // 检查活动状态
    if (activity.status !== 1) {
      return { canRegister: false, reason: '活动未发布' }
    }
    
    // 检查报名时间
    if (now > registrationEnd) {
      return { canRegister: false, reason: '报名已截止' }
    }
    
    // 检查活动是否已结束
    if (now > endTime) {
      return { canRegister: false, reason: '活动已结束' }
    }
    
    // 检查报名人数限制
    if (activity.quota > 0 && activity.registered_count >= activity.quota) {
      return { canRegister: false, reason: '报名已满' }
    }
    
    return { canRegister: true }
  } catch (error) {
    return { canRegister: false, reason: '活动信息获取失败' }
  }
}
