/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: H5用户端主入口文件
 */

import './assets/main.css'
import './styles/mobile.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

// 导入Vant组件库
import Vant from 'vant'
import 'vant/lib/index.css'

// 导入浏览器检测工具
import { addBrowserClass } from './utils/browser'

import App from './App.vue'
import router from './router'

// 添加浏览器类名
addBrowserClass()

const app = createApp(App)

// 使用Vant组件库
app.use(Vant)
app.use(createPinia())
app.use(router)

app.mount('#app')
