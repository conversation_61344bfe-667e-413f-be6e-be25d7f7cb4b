/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 报名状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Registration, RegistrationForm, RegistrationStatus, CodeSendStatus } from '../types/registration'
import { 
  sendVerificationCode, 
  submitRegistration, 
  checkRegistrationStatus,
  verifyCode 
} from '../api/registration'

export const useRegistrationStore = defineStore('registration', () => {
  // 状态
  const currentRegistration = ref<Registration | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const codeSendStatus = ref<CodeSendStatus>({
    canSend: true,
    countdown: 0
  })

  // 计算属性
  const isRegistered = computed(() => !!currentRegistration.value)

  // 方法
  const sendCode = async (phone: string, eventId: string | number) => {
    if (!codeSendStatus.value.canSend) {
      throw new Error(codeSendStatus.value.reason || '请稍后再试')
    }

    loading.value = true
    error.value = null

    try {
      await sendVerificationCode(phone, eventId)
      
      // 开始倒计时
      startCountdown()
      
    } catch (err) {
      error.value = err instanceof Error ? err.message : '发送验证码失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const startCountdown = () => {
    codeSendStatus.value = {
      canSend: false,
      countdown: 60,
      reason: '请稍后再试'
    }

    const timer = setInterval(() => {
      codeSendStatus.value.countdown--
      
      if (codeSendStatus.value.countdown <= 0) {
        clearInterval(timer)
        codeSendStatus.value = {
          canSend: true,
          countdown: 0
        }
      }
    }, 1000)
  }

  const submitForm = async (form: RegistrationForm) => {
    loading.value = true
    error.value = null

    try {
      const registration = await submitRegistration(form)
      currentRegistration.value = registration
      return registration
    } catch (err) {
      error.value = err instanceof Error ? err.message : '报名提交失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const checkStatus = async (phone: string, eventId: string | number) => {
    loading.value = true
    error.value = null

    try {
      const status = await checkRegistrationStatus(phone, eventId)
      if (status.registered && status.registration) {
        currentRegistration.value = status.registration
      }
      return status
    } catch (err) {
      error.value = err instanceof Error ? err.message : '查询报名状态失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const validateCode = async (phone: string, code: string, eventId: string | number) => {
    try {
      return await verifyCode(phone, code, eventId)
    } catch (err) {
      error.value = err instanceof Error ? err.message : '验证码验证失败'
      return false
    }
  }

  const clearRegistration = () => {
    currentRegistration.value = null
  }

  const clearError = () => {
    error.value = null
  }

  const resetCodeSendStatus = () => {
    codeSendStatus.value = {
      canSend: true,
      countdown: 0
    }
  }

  return {
    // 状态
    currentRegistration,
    loading,
    error,
    codeSendStatus,
    
    // 计算属性
    isRegistered,
    
    // 方法
    sendCode,
    submitForm,
    checkStatus,
    validateCode,
    clearRegistration,
    clearError,
    resetCodeSendStatus
  }
})
