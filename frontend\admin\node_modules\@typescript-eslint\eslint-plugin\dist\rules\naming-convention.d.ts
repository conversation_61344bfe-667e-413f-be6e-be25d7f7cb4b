import { TSESLint } from '@typescript-eslint/utils';
import type { Selector } from './naming-convention-utils';
export type MessageIds = 'doesNotMatchFormat' | 'doesNotMatchFormatTrimmed' | 'missingAffix' | 'missingUnderscore' | 'satisfyCustom' | 'unexpectedUnderscore';
export type Options = Selector[];
declare const _default: TSESLint.RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=naming-convention.d.ts.map