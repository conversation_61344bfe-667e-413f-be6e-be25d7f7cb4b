<!--
<AUTHOR>
@license http://www.178188.xyz
@lastmodify 2025年8月4日
模块说明: 活动详情页面
-->

<template>
  <div class="bg-gray-50 min-h-screen">
    <!-- 头部导航 -->
    <header class="bg-white shadow-sm sticky top-0 z-10">
      <div class="flex items-center px-4 py-3">
        <button class="back-btn mr-3 p-1 rounded-full hover:bg-gray-100 transition-colors" @click="goBack">
          <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">活动详情</h1>
      </div>
    </header>

    <!-- 加载状态 -->
    <div v-if="activityStore.loading" class="text-center py-8">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      <p class="mt-2 text-gray-500">加载中...</p>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="activityStore.error" class="text-center py-12">
      <div class="text-red-400 text-6xl mb-4">❌</div>
      <p class="text-red-500">{{ activityStore.error }}</p>
    </div>

    <!-- 活动内容 -->
    <div v-else-if="activity">
      <!-- 活动主要信息 -->
      <div class="bg-white border-b border-gray-200">
        <div class="px-4 py-6">
          <div class="flex justify-between items-start mb-4">
            <h1 class="text-xl font-bold text-gray-900 leading-tight pr-4">
              {{ activity.title }}
            </h1>
            <span :class="getStatusClass()" class="px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap">
              {{ getStatusText() }}
            </span>
          </div>
          
          <div class="space-y-3 text-gray-600">
            <div class="flex items-start">
              <svg class="w-5 h-5 mr-3 mt-0.5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              <div>
                <div class="font-medium text-gray-900">活动时间</div>
                <div class="text-sm">{{ formatTimeRange() }}</div>
              </div>
            </div>
            
            <div class="flex items-start">
              <svg class="w-5 h-5 mr-3 mt-0.5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <div>
                <div class="font-medium text-gray-900">活动地址</div>
                <div class="text-sm">{{ activity.address }}</div>
              </div>
            </div>
            
            <div class="flex items-start">
              <svg class="w-5 h-5 mr-3 mt-0.5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              <div>
                <div class="font-medium text-gray-900">参与对象</div>
                <div class="text-sm">{{ activity.target_audience }}</div>
              </div>
            </div>
            
            <div class="flex items-start">
              <svg class="w-5 h-5 mr-3 mt-0.5 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
              <div>
                <div class="font-medium text-gray-900">报名情况</div>
                <div class="text-sm">已报名 <span class="text-blue-600 font-semibold">{{ activity.registered_count }}</span>/{{ activity.quota || '不限' }}人</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 活动详细介绍 -->
      <div class="bg-white mt-2">
        <div class="px-4 py-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">活动介绍</h2>
          <div class="prose prose-sm text-gray-600 leading-relaxed">
            <div v-html="formatDescription()"></div>
          </div>
        </div>
      </div>

      <!-- 已报名提示 -->
      <div v-if="registrationStatus?.registered" class="registered-notice text-white mx-4 my-4 p-4 rounded-lg">
        <div class="flex items-center">
          <svg class="w-6 h-6 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <div>
            <div class="font-semibold">您已报名成功</div>
            <div class="text-sm opacity-90">{{ getRegistrationStatusText() }}</div>
          </div>
        </div>
      </div>

      <!-- 底部安全区域 -->
      <div class="h-20"></div>
    </div>

    <!-- 底部报名按钮 -->
    <div v-if="activity" class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-4">
      <button 
        :class="getButtonClass()"
        class="w-full py-4 rounded-lg text-lg font-semibold transition-colors"
        :disabled="!canRegister"
        @click="handleRegister"
      >
        {{ getButtonText() }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useActivityStore } from '../stores/activity'
import { useRegistrationStore } from '../stores/registration'
import type { Activity } from '../types/activity'
import type { RegistrationStatus } from '../types/registration'
import { ActivityStatus } from '../types/activity'

const route = useRoute()
const router = useRouter()
const activityStore = useActivityStore()
const registrationStore = useRegistrationStore()

// 响应式数据
const registrationStatus = ref<RegistrationStatus | null>(null)

// 计算属性
const activity = computed(() => activityStore.currentActivity)

const canRegister = computed(() => {
  if (!activity.value) return false

  const now = new Date()
  const end = new Date(activity.value.end_time)
  const registrationEnd = activity.value.registration_end
    ? new Date(activity.value.registration_end)
    : end

  return (
    activity.value.status === ActivityStatus.PUBLISHED &&
    now <= registrationEnd &&
    now <= end &&
    (activity.value.quota === 0 || activity.value.registered_count < activity.value.quota) &&
    (!registrationStatus.value?.registered)
  )
})

// 方法
const loadActivity = async () => {
  const id = route.params.id as string
  try {
    await activityStore.fetchActivityDetail(id)
  } catch (error) {
    console.error('加载活动详情失败:', error)
  }
}

const checkRegistrationStatus = async () => {
  const phone = localStorage.getItem('lastRegistrationPhone')
  if (phone && activity.value) {
    try {
      const status = await registrationStore.checkStatus(phone, activity.value.id)
      registrationStatus.value = status
    } catch (error) {
      console.error('检查报名状态失败:', error)
    }
  }
}

const goBack = () => {
  router.back()
}

const handleRegister = () => {
  if (!activity.value) return

  if (registrationStatus.value?.registered) {
    router.push(`/registration-status/${activity.value.id}`)
  } else {
    router.push(`/registration/${activity.value.id}`)
  }
}

const formatTimeRange = () => {
  if (!activity.value) return ''

  const start = new Date(activity.value.start_time)
  const end = new Date(activity.value.end_time)

  const startStr = start.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })

  const endStr = end.toLocaleString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })

  return `${startStr} - ${endStr}`
}

const formatDescription = () => {
  if (!activity.value?.description) return ''
  return activity.value.description.replace(/\n/g, '<br>')
}

const getStatusClass = () => {
  if (!activity.value) return ''

  const now = new Date()
  const start = new Date(activity.value.start_time)
  const end = new Date(activity.value.end_time)
  const regEnd = activity.value.registration_end ? new Date(activity.value.registration_end) : end

  if (now > end) return 'bg-gray-100 text-gray-500'
  if (activity.value.quota > 0 && activity.value.registered_count >= activity.value.quota) return 'bg-red-100 text-red-600'
  if (now > regEnd) return 'bg-gray-100 text-gray-500'
  if (start <= now && now <= regEnd) return 'bg-green-100 text-green-600'
  return 'bg-blue-100 text-blue-600'
}

const getStatusText = () => {
  if (!activity.value) return ''

  const now = new Date()
  const start = new Date(activity.value.start_time)
  const end = new Date(activity.value.end_time)
  const regEnd = activity.value.registration_end ? new Date(activity.value.registration_end) : end

  if (now > end) return '已结束'
  if (activity.value.quota > 0 && activity.value.registered_count >= activity.value.quota) return '已满员'
  if (now > regEnd) return '报名截止'
  if (start <= now && now <= regEnd) return '报名中'
  return '即将开始'
}

const getButtonText = () => {
  if (registrationStatus.value?.registered) {
    return '查看报名状态'
  }

  if (!canRegister.value) {
    if (!activity.value) return '加载中...'

    const now = new Date()
    const end = new Date(activity.value.end_time)

    if (now > end) return '活动已结束'
    if (activity.value.quota > 0 && activity.value.registered_count >= activity.value.quota) {
      return '报名已满'
    }
    return '报名已截止'
  }

  return '立即报名'
}

const getButtonClass = () => {
  if (canRegister.value && !registrationStatus.value?.registered) {
    return 'bg-blue-500 text-white hover:bg-blue-600 active:bg-blue-700'
  }
  return 'bg-gray-400 text-white cursor-not-allowed'
}

const getRegistrationStatusText = () => {
  if (!registrationStatus.value?.registration) return ''

  const phone = registrationStatus.value.registration.phone
  const maskedPhone = phone ? phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : ''
  return `报名手机号：${maskedPhone}`
}

// 生命周期
onMounted(async () => {
  await loadActivity()
  await checkRegistrationStatus()
})
</script>

<style scoped>
/* 微信浏览器兼容性 */
body {
  -webkit-text-size-adjust: 100%;
}

.back-btn:active {
  transform: scale(0.95);
}

.register-btn:active {
  transform: scale(0.98);
}

/* 已报名状态样式 */
.registered-notice {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
