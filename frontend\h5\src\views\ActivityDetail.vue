<!--
<AUTHOR>
@license http://www.178188.xyz
@lastmodify 2025年8月4日
模块说明: 活动详情页面
-->

<template>
  <div class="activity-detail">
    <!-- 导航栏 -->
    <van-nav-bar
      title="活动详情"
      left-arrow
      @click-left="goBack"
      fixed
    />
    
    <!-- 内容区域 -->
    <div class="content">
      <van-loading v-if="activityStore.loading" class="loading" />
      
      <div v-else-if="activity" class="detail-content">
        <!-- 活动标题 -->
        <div class="title-section">
          <h1 class="activity-title">{{ activity.title }}</h1>
          <van-tag :type="getStatusType()" class="status-tag">
            {{ getStatusText() }}
          </van-tag>
        </div>

        <!-- 活动信息 -->
        <div class="info-section">
          <div class="info-item">
            <van-icon name="clock-o" />
            <div class="info-content">
              <div class="info-label">活动时间</div>
              <div class="info-value">{{ formatTimeRange() }}</div>
            </div>
          </div>
          
          <div class="info-item">
            <van-icon name="location-o" />
            <div class="info-content">
              <div class="info-label">活动地点</div>
              <div class="info-value">{{ activity.address }}</div>
            </div>
          </div>
          
          <div class="info-item">
            <van-icon name="friends-o" />
            <div class="info-content">
              <div class="info-label">参与对象</div>
              <div class="info-value">{{ activity.target_audience }}</div>
            </div>
          </div>
          
          <div class="info-item">
            <van-icon name="user-o" />
            <div class="info-content">
              <div class="info-label">报名情况</div>
              <div class="info-value">
                {{ activity.registered_count }}/{{ activity.quota || '不限' }}人
                <span v-if="activity.quota > 0" class="quota-progress">
                  ({{ Math.round(activity.registered_count / activity.quota * 100) }}%)
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 活动描述 -->
        <div class="description-section">
          <h3 class="section-title">活动详情</h3>
          <div class="description-content" v-html="formatDescription()"></div>
        </div>

        <!-- 报名状态提示 -->
        <div v-if="registrationStatus" class="registration-status">
          <van-notice-bar
            :type="registrationStatus.registered ? 'success' : 'warning'"
            :text="getRegistrationStatusText()"
          />
        </div>
      </div>

      <!-- 错误状态 -->
      <van-empty
        v-else-if="activityStore.error"
        image="error"
        :description="activityStore.error"
      />
    </div>

    <!-- 底部操作栏 -->
    <div v-if="activity" class="bottom-action">
      <van-button
        :type="canRegister ? 'primary' : 'default'"
        :disabled="!canRegister"
        :loading="registrationStore.loading"
        block
        round
        @click="handleRegister"
      >
        {{ getButtonText() }}
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useActivityStore } from '../stores/activity'
import { useRegistrationStore } from '../stores/registration'
import type { Activity } from '../types/activity'
import type { RegistrationStatus } from '../types/registration'
import { ActivityStatus } from '../types/activity'

const route = useRoute()
const router = useRouter()
const activityStore = useActivityStore()
const registrationStore = useRegistrationStore()

// 响应式数据
const registrationStatus = ref<RegistrationStatus | null>(null)

// 计算属性
const activity = computed(() => activityStore.currentActivity)

const canRegister = computed(() => {
  if (!activity.value) return false
  
  const now = new Date()
  const end = new Date(activity.value.end_time)
  const registrationEnd = activity.value.registration_end 
    ? new Date(activity.value.registration_end) 
    : end
  
  return (
    activity.value.status === ActivityStatus.PUBLISHED &&
    now <= registrationEnd &&
    now <= end &&
    (activity.value.quota === 0 || activity.value.registered_count < activity.value.quota) &&
    (!registrationStatus.value?.registered)
  )
})

// 方法
const loadActivity = async () => {
  const id = route.params.id as string
  try {
    await activityStore.fetchActivityDetail(id)
  } catch (error) {
    console.error('加载活动详情失败:', error)
  }
}

const checkRegistrationStatus = async () => {
  const phone = localStorage.getItem('lastRegistrationPhone')
  if (phone && activity.value) {
    try {
      const status = await registrationStore.checkStatus(phone, activity.value.id)
      registrationStatus.value = status
    } catch (error) {
      console.error('检查报名状态失败:', error)
    }
  }
}

const goBack = () => {
  router.back()
}

const handleRegister = () => {
  if (!activity.value) return
  
  if (registrationStatus.value?.registered) {
    router.push(`/registration-status/${activity.value.id}`)
  } else {
    router.push(`/registration/${activity.value.id}`)
  }
}

const formatTimeRange = () => {
  if (!activity.value) return ''
  
  const start = new Date(activity.value.start_time)
  const end = new Date(activity.value.end_time)
  
  const startStr = start.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
  
  const endStr = end.toLocaleString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
  
  return `${startStr} - ${endStr}`
}

const formatDescription = () => {
  if (!activity.value?.description) return ''
  return activity.value.description.replace(/\n/g, '<br>')
}

const getStatusType = () => {
  if (!activity.value) return 'default'
  
  const now = new Date()
  const start = new Date(activity.value.start_time)
  const end = new Date(activity.value.end_time)
  
  if (now > end) return 'default'
  if (start <= now && now <= end) return 'success'
  return 'primary'
}

const getStatusText = () => {
  if (!activity.value) return ''
  
  const now = new Date()
  const start = new Date(activity.value.start_time)
  const end = new Date(activity.value.end_time)
  
  if (now > end) return '已结束'
  if (start <= now && now <= end) return '进行中'
  return '即将开始'
}

const getButtonText = () => {
  if (registrationStatus.value?.registered) {
    return '查看报名状态'
  }
  
  if (!canRegister.value) {
    if (!activity.value) return '加载中...'
    
    const now = new Date()
    const end = new Date(activity.value.end_time)
    
    if (now > end) return '活动已结束'
    if (activity.value.quota > 0 && activity.value.registered_count >= activity.value.quota) {
      return '报名已满'
    }
    return '报名已截止'
  }
  
  return '立即报名'
}

const getRegistrationStatusText = () => {
  if (!registrationStatus.value) return ''
  
  if (registrationStatus.value.registered) {
    const phone = registrationStatus.value.registration?.phone
    const maskedPhone = phone ? phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : ''
    return `您已报名成功，报名手机号：${maskedPhone}`
  }
  
  return ''
}

// 生命周期
onMounted(async () => {
  await loadActivity()
  await checkRegistrationStatus()
})
</script>

<style scoped>
.activity-detail {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px; /* 底部按钮高度 */
}

.content {
  padding-top: 46px; /* 导航栏高度 */
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.detail-content {
  padding: 16px;
}

.title-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.activity-title {
  font-size: 20px;
  font-weight: 600;
  color: #323233;
  line-height: 1.4;
  flex: 1;
  margin-right: 12px;
}

.status-tag {
  flex-shrink: 0;
}

.info-section {
  background: white;
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 16px 20px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .van-icon {
  margin-right: 12px;
  margin-top: 2px;
  color: #1989fa;
  font-size: 18px;
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 14px;
  color: #969799;
  margin-bottom: 4px;
}

.info-value {
  font-size: 16px;
  color: #323233;
  line-height: 1.4;
}

.quota-progress {
  color: #1989fa;
  font-size: 14px;
}

.description-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 12px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 12px;
}

.description-content {
  font-size: 14px;
  color: #646566;
  line-height: 1.6;
}

.registration-status {
  margin-bottom: 12px;
}

.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: white;
  border-top: 1px solid #ebedf0;
  z-index: 100;
}
</style>
