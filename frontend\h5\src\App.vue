<!--
<AUTHOR>
@license http://www.178188.xyz
@lastmodify 2025年8月4日
模块说明: H5用户端主应用组件
-->

<script setup lang="ts">
import { RouterView } from 'vue-router'
import { ConfigProvider } from 'vant'

// Vant主题配置
const themeVars = {
  primaryColor: '#1989fa',
  successColor: '#07c160',
  warningColor: '#ff976a',
  dangerColor: '#ee0a24',
  textColor: '#323233',
  textColor2: '#646566',
  textColor3: '#969799',
  backgroundColor: '#f7f8fa',
  backgroundColor2: '#ffffff'
}
</script>

<template>
  <ConfigProvider :theme-vars="themeVars">
    <div id="app">
      <RouterView />
    </div>
  </ConfigProvider>
</template>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f7f8fa;
}

#app {
  min-height: 100vh;
  width: 100%;
}

/* 移动端适配 */
@media screen and (max-width: 750px) {
  html {
    font-size: 14px;
  }
}

/* 微信浏览器适配 */
.ios-fix {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 防止页面缩放 */
input, textarea {
  font-size: 16px;
}

/* 隐藏滚动条但保持滚动功能 */
::-webkit-scrollbar {
  display: none;
}

/* 自定义Vant组件样式 */
.van-nav-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.van-nav-bar__title {
  color: white;
  font-weight: 600;
}

.van-nav-bar .van-icon {
  color: white;
}

.van-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.van-button--primary:active {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}
</style>
