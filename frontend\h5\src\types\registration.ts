/**
 * <AUTHOR>
 * @license http://www.178188.xyz
 * @lastmodify 2025年8月4日
 * 模块说明: 报名相关类型定义
 */

/**
 * 短信状态枚举
 */
export enum SmsStatus {
  PENDING = 0,  // 待发送
  SUCCESS = 1,  // 发送成功
  FAILED = 2    // 发送失败
}

/**
 * 报名记录数据结构
 */
export interface Registration {
  id: number
  event_id: number
  phone: string
  sms_status: SmsStatus
  sms_sent_at?: string
  created_at: string
}

/**
 * 报名表单数据
 */
export interface RegistrationForm {
  phone: string
  code: string
  eventId: string | number
}

/**
 * 报名状态查询结果
 */
export interface RegistrationStatus {
  registered: boolean
  registration?: Registration
}

/**
 * 验证码发送状态
 */
export interface CodeSendStatus {
  canSend: boolean
  countdown: number
  reason?: string
}
