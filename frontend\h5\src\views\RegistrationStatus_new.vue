<!--
<AUTHOR>
@license http://www.178188.xyz
@lastmodify 2025年8月4日
模块说明: 报名状态页面
-->

<template>
  <div class="bg-gray-50 min-h-screen">
    <!-- 头部导航 -->
    <header class="bg-white shadow-sm sticky top-0 z-10">
      <div class="flex items-center px-4 py-3">
        <button class="back-btn mr-3 p-1 rounded-full hover:bg-gray-100 transition-colors" @click="goBack">
          <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </button>
        <h1 class="text-lg font-semibold text-gray-900">报名状态</h1>
      </div>
    </header>

    <!-- 加载状态 -->
    <div v-if="loading" class="text-center py-8">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      <p class="mt-2 text-gray-500">加载中...</p>
    </div>
    
    <!-- 成功状态 -->
    <div v-else-if="registrationStatus?.registered" class="px-4 py-6">
      <!-- 成功提示 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-4">
        <div class="text-center">
          <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h2 class="text-xl font-semibold text-gray-900 mb-2">报名成功</h2>
          <p class="text-gray-600">您已成功报名此活动</p>
        </div>
      </div>

      <!-- 活动信息 -->
      <div v-if="activity" class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ activity.title }}</h3>
        <div class="space-y-2 text-sm text-gray-600">
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            <span>{{ formatTimeRange() }}</span>
          </div>
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <span>{{ activity.address }}</span>
          </div>
          <div class="flex items-center">
            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            <span>{{ activity.target_audience }}</span>
          </div>
        </div>
      </div>

      <!-- 报名信息 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-3">报名信息</h3>
        <div class="space-y-3">
          <div class="flex justify-between items-center py-2 border-b border-gray-100">
            <span class="text-gray-600">报名手机号</span>
            <span class="font-medium">{{ maskedPhone }}</span>
          </div>
          <div class="flex justify-between items-center py-2 border-b border-gray-100">
            <span class="text-gray-600">报名时间</span>
            <span class="font-medium">{{ formatRegistrationTime() }}</span>
          </div>
          <div class="flex justify-between items-center py-2">
            <span class="text-gray-600">短信状态</span>
            <span :class="getSmsStatusClass()" class="px-2 py-1 rounded-full text-xs font-medium">
              {{ getSmsStatusText() }}
            </span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="space-y-3">
        <button 
          class="w-full bg-blue-500 text-white py-4 rounded-lg text-lg font-semibold hover:bg-blue-600 transition-colors"
          @click="goToActivityDetail"
        >
          查看活动详情
        </button>
        
        <button 
          class="w-full bg-gray-100 text-gray-700 py-4 rounded-lg text-lg font-semibold hover:bg-gray-200 transition-colors"
          @click="goToActivityList"
        >
          浏览更多活动
        </button>
      </div>
    </div>
    
    <!-- 未报名状态 -->
    <div v-else class="px-4 py-6">
      <!-- 未找到提示 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-4">
        <div class="text-center">
          <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h2 class="text-xl font-semibold text-gray-900 mb-2">未找到报名记录</h2>
          <p class="text-gray-600">请确认手机号是否正确，或重新报名</p>
        </div>
      </div>

      <!-- 查询表单 -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">查询报名状态</h3>
        <div class="space-y-4">
          <div>
            <label for="query-phone" class="block text-sm font-medium text-gray-700 mb-2">
              手机号码
            </label>
            <input 
              v-model="queryPhone"
              type="tel" 
              id="query-phone" 
              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 focus:ring-3 focus:ring-blue-100 transition-all"
              placeholder="请输入报名手机号"
              maxlength="11"
            >
          </div>
          
          <button 
            :class="[
              'w-full py-4 rounded-lg text-lg font-semibold transition-colors',
              isValidPhone ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            ]"
            :disabled="!isValidPhone || loading"
            @click="handleQuery"
          >
            {{ loading ? '查询中...' : '查询报名状态' }}
          </button>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="space-y-3">
        <button 
          class="w-full bg-blue-500 text-white py-4 rounded-lg text-lg font-semibold hover:bg-blue-600 transition-colors"
          @click="goToRegistration"
        >
          立即报名
        </button>
        
        <button 
          class="w-full bg-gray-100 text-gray-700 py-4 rounded-lg text-lg font-semibold hover:bg-gray-200 transition-colors"
          @click="goToActivityList"
        >
          浏览更多活动
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useActivityStore } from '../stores/activity'
import { useRegistrationStore } from '../stores/registration'
import type { RegistrationStatus } from '../types/registration'
import { SmsStatus } from '../types/registration'

const route = useRoute()
const router = useRouter()
const activityStore = useActivityStore()
const registrationStore = useRegistrationStore()

// 响应式数据
const loading = ref(false)
const registrationStatus = ref<RegistrationStatus | null>(null)
const queryPhone = ref('')

// 计算属性
const activity = computed(() => activityStore.currentActivity)

const maskedPhone = computed(() => {
  const phone = registrationStatus.value?.registration?.phone
  return phone ? phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : ''
})

const isValidPhone = computed(() => {
  return /^1[3-9]\d{9}$/.test(queryPhone.value)
})

// 方法
const loadData = async () => {
  const eventId = route.params.id as string

  loading.value = true

  try {
    // 加载活动详情
    await activityStore.fetchActivityDetail(eventId)

    // 尝试从本地存储获取手机号
    const savedPhone = localStorage.getItem('lastRegistrationPhone')
    if (savedPhone) {
      queryPhone.value = savedPhone
      await checkRegistrationStatus(savedPhone, eventId)
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    alert('加载失败，请重试')
  } finally {
    loading.value = false
  }
}

const checkRegistrationStatus = async (phone: string, eventId: string) => {
  try {
    const status = await registrationStore.checkStatus(phone, eventId)
    registrationStatus.value = status
  } catch (error) {
    console.error('查询报名状态失败:', error)
    registrationStatus.value = { registered: false }
  }
}

const handleQuery = async () => {
  if (!isValidPhone.value) return

  const eventId = route.params.id as string
  loading.value = true

  try {
    await checkRegistrationStatus(queryPhone.value, eventId)

    if (registrationStatus.value?.registered) {
      // 保存手机号到本地存储
      localStorage.setItem('lastRegistrationPhone', queryPhone.value)
      alert('查询成功')
    } else {
      alert('未找到报名记录')
    }
  } catch (error) {
    alert('查询失败，请重试')
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.back()
}

const goToActivityDetail = () => {
  if (activity.value) {
    router.push(`/activity/${activity.value.id}`)
  }
}

const goToActivityList = () => {
  router.push('/')
}

const goToRegistration = () => {
  if (activity.value) {
    router.push(`/registration/${activity.value.id}`)
  }
}

const formatTimeRange = () => {
  if (!activity.value) return ''

  const start = new Date(activity.value.start_time)
  const end = new Date(activity.value.end_time)

  const startStr = start.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })

  const endStr = end.toLocaleString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })

  return `${startStr} - ${endStr}`
}

const formatRegistrationTime = () => {
  const time = registrationStatus.value?.registration?.created_at
  if (!time) return ''

  return new Date(time).toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getSmsStatusText = () => {
  const status = registrationStatus.value?.registration?.sms_status
  switch (status) {
    case SmsStatus.SUCCESS:
      return '发送成功'
    case SmsStatus.FAILED:
      return '发送失败'
    case SmsStatus.PENDING:
    default:
      return '待发送'
  }
}

const getSmsStatusClass = () => {
  const status = registrationStatus.value?.registration?.sms_status
  switch (status) {
    case SmsStatus.SUCCESS:
      return 'bg-green-100 text-green-600'
    case SmsStatus.FAILED:
      return 'bg-red-100 text-red-600'
    case SmsStatus.PENDING:
    default:
      return 'bg-yellow-100 text-yellow-600'
  }
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
/* 微信浏览器兼容性 */
body {
  -webkit-text-size-adjust: 100%;
}

.back-btn:active {
  transform: scale(0.95);
}

/* 输入框焦点样式 */
input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
